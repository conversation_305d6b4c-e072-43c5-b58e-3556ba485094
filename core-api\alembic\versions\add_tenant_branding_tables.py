"""Add tenant branding tables

Revision ID: add_tenant_branding
Revises: previous_revision
Create Date: 2024-01-25 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_tenant_branding'
down_revision = 'previous_revision'  # Replace with actual previous revision
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create tenant_branding table
    op.create_table(
        'tenant_branding',
        sa.Column('branding_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('organization_name', sa.String(length=255), nullable=False),
        sa.Column('display_name', sa.String(length=255), nullable=True),
        sa.Column('description', sa.String(length=1000), nullable=True),
        sa.Column('website', sa.String(length=255), nullable=True),
        sa.Column('subdomain', sa.String(length=100), nullable=False),
        sa.Column('custom_domain', sa.String(length=255), nullable=True),
        sa.Column('primary_color', sa.String(length=7), nullable=False, default='#3B82F6'),
        sa.Column('secondary_color', sa.String(length=7), nullable=False, default='#10B981'),
        sa.Column('accent_color', sa.String(length=7), nullable=False, default='#F59E0B'),
        sa.Column('background_color', sa.String(length=7), nullable=False, default='#FFFFFF'),
        sa.Column('text_color', sa.String(length=7), nullable=False, default='#1F2937'),
        sa.Column('logo_url', sa.String(length=500), nullable=True),
        sa.Column('logo_light_url', sa.String(length=500), nullable=True),
        sa.Column('logo_dark_url', sa.String(length=500), nullable=True),
        sa.Column('favicon_url', sa.String(length=500), nullable=True),
        sa.Column('login_background_url', sa.String(length=500), nullable=True),
        sa.Column('font_family', sa.String(length=100), nullable=False, default='Inter'),
        sa.Column('border_radius', sa.String(length=20), nullable=False, default='8px'),
        sa.Column('header_style', sa.Enum('default', 'minimal', 'bold', name='headerstyle'), nullable=False, default='default'),
        sa.Column('sidebar_style', sa.Enum('default', 'compact', 'expanded', name='sidebarstyle'), nullable=False, default='default'),
        sa.Column('card_style', sa.Enum('default', 'elevated', 'flat', name='cardstyle'), nullable=False, default='default'),
        sa.Column('button_style', sa.Enum('default', 'rounded', 'square', name='buttonstyle'), nullable=False, default='default'),
        sa.Column('custom_css', sa.Text(), nullable=True),
        sa.Column('custom_js', sa.Text(), nullable=True),
        sa.Column('custom_footer', sa.Text(), nullable=True),
        sa.Column('hide_arroyo_branding', sa.Boolean(), nullable=False, default=False),
        sa.Column('enable_white_label', sa.Boolean(), nullable=False, default=False),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('version', sa.Integer(), nullable=False, default=1),
        sa.Column('logo_file_size', sa.Integer(), nullable=True),
        sa.Column('favicon_file_size', sa.Integer(), nullable=True),
        sa.Column('background_file_size', sa.Integer(), nullable=True),
        sa.Column('last_applied_at', sa.DateTime(), nullable=True),
        sa.Column('applied_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.PrimaryKeyConstraint('branding_id'),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.tenant_id'], ),
        sa.ForeignKeyConstraint(['applied_by'], ['users.user_id'], ),
    )
    
    # Create indexes
    op.create_index('ix_tenant_branding_tenant_id', 'tenant_branding', ['tenant_id'])
    op.create_index('ix_tenant_branding_subdomain', 'tenant_branding', ['subdomain'])
    op.create_index('ix_tenant_branding_is_active', 'tenant_branding', ['is_active'])
    
    # Create unique constraint for active branding per tenant
    op.create_index(
        'ix_tenant_branding_unique_active',
        'tenant_branding',
        ['tenant_id'],
        unique=True,
        postgresql_where=sa.text('is_active = true')
    )
    
    # Create tenant_branding_history table
    op.create_table(
        'tenant_branding_history',
        sa.Column('history_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('branding_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('branding_data', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('changed_by', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('change_reason', sa.String(length=500), nullable=True),
        sa.Column('version', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.PrimaryKeyConstraint('history_id'),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.tenant_id'], ),
        sa.ForeignKeyConstraint(['branding_id'], ['tenant_branding.branding_id'], ),
        sa.ForeignKeyConstraint(['changed_by'], ['users.user_id'], ),
    )
    
    # Create indexes for history table
    op.create_index('ix_tenant_branding_history_tenant_id', 'tenant_branding_history', ['tenant_id'])
    op.create_index('ix_tenant_branding_history_branding_id', 'tenant_branding_history', ['branding_id'])
    op.create_index('ix_tenant_branding_history_created_at', 'tenant_branding_history', ['created_at'])


def downgrade() -> None:
    # Drop indexes first
    op.drop_index('ix_tenant_branding_history_created_at', table_name='tenant_branding_history')
    op.drop_index('ix_tenant_branding_history_branding_id', table_name='tenant_branding_history')
    op.drop_index('ix_tenant_branding_history_tenant_id', table_name='tenant_branding_history')
    
    # Drop history table
    op.drop_table('tenant_branding_history')
    
    # Drop branding table indexes
    op.drop_index('ix_tenant_branding_unique_active', table_name='tenant_branding')
    op.drop_index('ix_tenant_branding_is_active', table_name='tenant_branding')
    op.drop_index('ix_tenant_branding_subdomain', table_name='tenant_branding')
    op.drop_index('ix_tenant_branding_tenant_id', table_name='tenant_branding')
    
    # Drop branding table
    op.drop_table('tenant_branding')
    
    # Drop enums
    op.execute('DROP TYPE IF EXISTS headerstyle')
    op.execute('DROP TYPE IF EXISTS sidebarstyle')
    op.execute('DROP TYPE IF EXISTS cardstyle')
    op.execute('DROP TYPE IF EXISTS buttonstyle')
