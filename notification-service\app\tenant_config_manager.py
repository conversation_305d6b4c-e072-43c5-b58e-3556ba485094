"""
Tenant Configuration Manager for Notification Service
Handles loading and using tenant-specific notification configurations
"""

import asyncio
import aiohttp
from typing import Optional, Dict, Any, List
from uuid import UUID
from datetime import datetime, timedelta
import logging

from .config import settings

logger = logging.getLogger(__name__)


class TenantConfigManager:
    """Manages tenant-specific notification configurations"""
    
    def __init__(self):
        self.config_cache = {}
        self.cache_ttl = timedelta(minutes=15)  # Cache configs for 15 minutes
        self.core_api_url = settings.CORE_API_URL
    
    async def get_tenant_config(
        self, 
        tenant_id: UUID, 
        channel_type: str,
        force_refresh: bool = False
    ) -> Optional[Dict[str, Any]]:
        """Get tenant-specific configuration for a notification channel"""
        
        cache_key = f"{tenant_id}:{channel_type}"
        
        # Check cache first (unless force refresh)
        if not force_refresh and cache_key in self.config_cache:
            cached_config, cached_at = self.config_cache[cache_key]
            if datetime.utcnow() - cached_at < self.cache_ttl:
                return cached_config
        
        # Fetch from core API
        try:
            config = await self._fetch_tenant_config(tenant_id, channel_type)
            
            # Cache the result
            self.config_cache[cache_key] = (config, datetime.utcnow())
            
            return config
            
        except Exception as e:
            logger.error(f"Failed to fetch tenant config for {tenant_id}:{channel_type}: {e}")
            
            # Return cached config if available, even if expired
            if cache_key in self.config_cache:
                cached_config, _ = self.config_cache[cache_key]
                logger.warning(f"Using expired cached config for {tenant_id}:{channel_type}")
                return cached_config
            
            return None
    
    async def _fetch_tenant_config(self, tenant_id: UUID, channel_type: str) -> Optional[Dict[str, Any]]:
        """Fetch tenant configuration from core API"""
        
        if not settings.ENABLE_TENANT_CONFIGS:
            return None
        
        url = f"{self.core_api_url}/api/v1/tenant/notifications/configs"
        params = {
            "channel_type": channel_type,
            "is_enabled": True
        }
        
        headers = {
            "X-Tenant-ID": str(tenant_id),
            "Content-Type": "application/json"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params, headers=headers) as response:
                if response.status == 200:
                    configs = await response.json()
                    
                    # Return the highest priority enabled config
                    if configs:
                        # Sort by priority (1 = highest priority)
                        configs.sort(key=lambda x: x.get('priority', 10))
                        return configs[0]
                
                elif response.status == 404:
                    # No tenant-specific config found
                    return None
                
                else:
                    logger.error(f"Failed to fetch tenant config: HTTP {response.status}")
                    return None
    
    async def get_email_config(self, tenant_id: UUID) -> Dict[str, Any]:
        """Get email configuration for tenant"""
        
        tenant_config = await self.get_tenant_config(tenant_id, "email")
        
        if tenant_config and tenant_config.get("config_data"):
            config_data = tenant_config["config_data"]
            return {
                "smtp_host": config_data.get("smtp_host"),
                "smtp_port": config_data.get("smtp_port", 587),
                "smtp_user": config_data.get("smtp_user"),
                "smtp_password": config_data.get("smtp_password"),
                "smtp_tls": config_data.get("smtp_tls", True),
                "smtp_ssl": config_data.get("smtp_ssl", False),
                "from_email": config_data.get("from_email"),
                "from_name": config_data.get("from_name", "Arroyo University"),
                "timeout": config_data.get("smtp_timeout", 30)
            }
        
        # Fallback to global configuration
        if settings.FALLBACK_TO_GLOBAL_CONFIG:
            return {
                "smtp_host": settings.GLOBAL_SMTP_HOST,
                "smtp_port": settings.GLOBAL_SMTP_PORT,
                "smtp_user": settings.GLOBAL_SMTP_USER,
                "smtp_password": settings.GLOBAL_SMTP_PASSWORD,
                "smtp_tls": settings.GLOBAL_SMTP_TLS,
                "smtp_ssl": settings.GLOBAL_SMTP_SSL,
                "from_email": settings.GLOBAL_FROM_EMAIL,
                "from_name": settings.GLOBAL_FROM_NAME,
                "timeout": settings.GLOBAL_SMTP_TIMEOUT
            }
        
        return None
    
    async def get_sms_config(self, tenant_id: UUID) -> Dict[str, Any]:
        """Get SMS configuration for tenant"""
        
        tenant_config = await self.get_tenant_config(tenant_id, "sms")
        
        if tenant_config and tenant_config.get("config_data"):
            config_data = tenant_config["config_data"]
            return {
                "account_sid": config_data.get("account_sid"),
                "auth_token": config_data.get("auth_token"),
                "phone_number": config_data.get("phone_number"),
                "messaging_service_sid": config_data.get("messaging_service_sid")
            }
        
        # Fallback to global configuration
        if settings.FALLBACK_TO_GLOBAL_CONFIG:
            return {
                "account_sid": settings.GLOBAL_TWILIO_ACCOUNT_SID,
                "auth_token": settings.GLOBAL_TWILIO_AUTH_TOKEN,
                "phone_number": settings.GLOBAL_TWILIO_PHONE_NUMBER,
                "messaging_service_sid": None
            }
        
        return None
    
    async def get_slack_config(self, tenant_id: UUID) -> Dict[str, Any]:
        """Get Slack configuration for tenant"""
        
        tenant_config = await self.get_tenant_config(tenant_id, "slack")
        
        if tenant_config and tenant_config.get("config_data"):
            config_data = tenant_config["config_data"]
            return {
                "bot_token": config_data.get("bot_token"),
                "signing_secret": config_data.get("signing_secret"),
                "default_channel": config_data.get("default_channel", "#general")
            }
        
        # Fallback to global configuration
        if settings.FALLBACK_TO_GLOBAL_CONFIG:
            return {
                "bot_token": settings.GLOBAL_SLACK_BOT_TOKEN,
                "signing_secret": settings.GLOBAL_SLACK_SIGNING_SECRET,
                "default_channel": settings.GLOBAL_DEFAULT_SLACK_CHANNEL
            }
        
        return None
    
    async def get_teams_config(self, tenant_id: UUID) -> Dict[str, Any]:
        """Get Microsoft Teams configuration for tenant"""
        
        tenant_config = await self.get_tenant_config(tenant_id, "teams")
        
        if tenant_config and tenant_config.get("config_data"):
            config_data = tenant_config["config_data"]
            return {
                "webhook_url": config_data.get("webhook_url"),
                "theme_color": config_data.get("theme_color", "#0078D4")
            }
        
        # Fallback to global configuration
        if settings.FALLBACK_TO_GLOBAL_CONFIG:
            return {
                "webhook_url": settings.GLOBAL_TEAMS_WEBHOOK_URL,
                "theme_color": "#0078D4"
            }
        
        return None
    
    async def get_webhook_config(self, tenant_id: UUID) -> Dict[str, Any]:
        """Get webhook configuration for tenant"""
        
        tenant_config = await self.get_tenant_config(tenant_id, "webhook")
        
        if tenant_config and tenant_config.get("config_data"):
            config_data = tenant_config["config_data"]
            return {
                "url": config_data.get("url"),
                "method": config_data.get("method", "POST"),
                "headers": config_data.get("headers", {}),
                "secret": config_data.get("secret"),
                "timeout": config_data.get("timeout", 30)
            }
        
        return None
    
    async def is_channel_enabled(self, tenant_id: UUID, channel_type: str) -> bool:
        """Check if a notification channel is enabled for tenant"""
        
        tenant_config = await self.get_tenant_config(tenant_id, channel_type)
        
        if tenant_config:
            return tenant_config.get("is_enabled", False)
        
        # Fallback to global settings
        if settings.FALLBACK_TO_GLOBAL_CONFIG:
            global_enabled_map = {
                "email": settings.ENABLE_EMAIL_NOTIFICATIONS,
                "sms": settings.GLOBAL_ENABLE_SMS,
                "slack": settings.GLOBAL_ENABLE_SLACK_NOTIFICATIONS,
                "teams": settings.GLOBAL_ENABLE_TEAMS_NOTIFICATIONS,
                "webhook": settings.ENABLE_WEBHOOK_NOTIFICATIONS,
                "push": settings.ENABLE_PUSH_NOTIFICATIONS
            }
            return global_enabled_map.get(channel_type, False)
        
        return False
    
    async def update_usage_stats(self, tenant_id: UUID, config_id: UUID, sent: int = 0, failed: int = 0):
        """Update usage statistics for a tenant configuration"""
        
        try:
            url = f"{self.core_api_url}/api/v1/tenant/notifications/configs/{config_id}/usage"
            data = {
                "sent": sent,
                "failed": failed,
                "last_used_at": datetime.utcnow().isoformat()
            }
            
            headers = {
                "X-Tenant-ID": str(tenant_id),
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=data, headers=headers) as response:
                    if response.status != 200:
                        logger.warning(f"Failed to update usage stats: HTTP {response.status}")
        
        except Exception as e:
            logger.error(f"Failed to update usage stats: {e}")
    
    def clear_cache(self, tenant_id: Optional[UUID] = None):
        """Clear configuration cache"""
        
        if tenant_id:
            # Clear cache for specific tenant
            keys_to_remove = [key for key in self.config_cache.keys() if key.startswith(str(tenant_id))]
            for key in keys_to_remove:
                del self.config_cache[key]
        else:
            # Clear all cache
            self.config_cache.clear()


# Global instance
tenant_config_manager = TenantConfigManager()
