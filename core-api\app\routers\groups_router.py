"""
Groups Router
Handles group-related API endpoints including group management, membership, and activities
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlmodel import Session
from typing import List, Optional
from uuid import UUID

from ..core.database import get_session
from ..core.security import get_current_user, require_permissions
from ..models.user_models import User
from ..models.group_models import (
    Group, GroupCreate, GroupUpdate,
    GroupMembership, GroupMembershipCreate,
    GroupInvitation, GroupInvitationCreate,
    GroupActivity, GroupActivityCreate
)
from ..services.group_service import GroupService

router = APIRouter()

# Groups
@router.get("/", response_model=List[Group])
async def get_groups(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    is_public: Optional[bool] = None,
    search: Optional[str] = None,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get groups with optional filtering"""
    group_service = GroupService(session)
    return await group_service.get_groups(
        tenant_id=current_user.tenant_id,
        skip=skip,
        limit=limit,
        is_public=is_public,
        search=search
    )

@router.post("/", response_model=Group, status_code=status.HTTP_201_CREATED)
async def create_group(
    group_data: GroupCreate,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["group_create"]))
):
    """Create a new group"""
    group_service = GroupService(session)
    return await group_service.create_group(group_data, current_user.id)

@router.get("/{group_id}", response_model=Group)
async def get_group(
    group_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get a specific group"""
    group_service = GroupService(session)
    group = await group_service.get_group(group_id)
    if not group:
        raise HTTPException(status_code=404, detail="Group not found")
    
    # Check if user can view this group
    if not group.is_public and not await group_service.is_member(group_id, current_user.id):
        raise HTTPException(status_code=403, detail="Not authorized to view this group")
    
    return group

@router.put("/{group_id}", response_model=Group)
async def update_group(
    group_id: UUID,
    group_data: GroupUpdate,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Update a group"""
    group_service = GroupService(session)
    group = await group_service.get_group(group_id)
    if not group:
        raise HTTPException(status_code=404, detail="Group not found")
    
    # Check if user is the leader or has group management permissions
    if group.leader_id != current_user.id and not current_user.has_permission("group_manage"):
        raise HTTPException(status_code=403, detail="Not authorized to update this group")
    
    updated_group = await group_service.update_group(group_id, group_data)
    return updated_group

@router.delete("/{group_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_group(
    group_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Delete a group"""
    group_service = GroupService(session)
    group = await group_service.get_group(group_id)
    if not group:
        raise HTTPException(status_code=404, detail="Group not found")
    
    # Check if user is the leader or has group management permissions
    if group.leader_id != current_user.id and not current_user.has_permission("group_manage"):
        raise HTTPException(status_code=403, detail="Not authorized to delete this group")
    
    success = await group_service.delete_group(group_id)
    if not success:
        raise HTTPException(status_code=404, detail="Group not found")

# Group Membership
@router.get("/{group_id}/members", response_model=List[GroupMembership])
async def get_group_members(
    group_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get group members"""
    group_service = GroupService(session)
    
    # Check if user can view group members
    group = await group_service.get_group(group_id)
    if not group:
        raise HTTPException(status_code=404, detail="Group not found")
    
    if not group.is_public and not await group_service.is_member(group_id, current_user.id):
        raise HTTPException(status_code=403, detail="Not authorized to view group members")
    
    return await group_service.get_members(group_id, skip=skip, limit=limit)

@router.post("/{group_id}/join", response_model=GroupMembership, status_code=status.HTTP_201_CREATED)
async def join_group(
    group_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Join a public group"""
    group_service = GroupService(session)
    group = await group_service.get_group(group_id)
    if not group:
        raise HTTPException(status_code=404, detail="Group not found")
    
    if not group.is_public:
        raise HTTPException(status_code=403, detail="Cannot join private group without invitation")
    
    membership = await group_service.add_member(group_id, current_user.id)
    if not membership:
        raise HTTPException(status_code=400, detail="Already a member of this group")
    
    return membership

@router.delete("/{group_id}/leave", status_code=status.HTTP_204_NO_CONTENT)
async def leave_group(
    group_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Leave a group"""
    group_service = GroupService(session)
    success = await group_service.remove_member(group_id, current_user.id)
    if not success:
        raise HTTPException(status_code=400, detail="Not a member of this group")

@router.delete("/{group_id}/members/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def remove_group_member(
    group_id: UUID,
    user_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Remove a member from the group"""
    group_service = GroupService(session)
    group = await group_service.get_group(group_id)
    if not group:
        raise HTTPException(status_code=404, detail="Group not found")
    
    # Check if user is the leader or has group management permissions
    if group.leader_id != current_user.id and not current_user.has_permission("group_manage"):
        raise HTTPException(status_code=403, detail="Not authorized to remove members")
    
    success = await group_service.remove_member(group_id, user_id)
    if not success:
        raise HTTPException(status_code=400, detail="User is not a member of this group")

# Group Invitations
@router.get("/{group_id}/invitations", response_model=List[GroupInvitation])
async def get_group_invitations(
    group_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get group invitations (leader only)"""
    group_service = GroupService(session)
    group = await group_service.get_group(group_id)
    if not group:
        raise HTTPException(status_code=404, detail="Group not found")
    
    # Check if user is the leader
    if group.leader_id != current_user.id:
        raise HTTPException(status_code=403, detail="Only group leader can view invitations")
    
    return await group_service.get_invitations(group_id, skip=skip, limit=limit)

@router.post("/{group_id}/invitations", response_model=GroupInvitation, status_code=status.HTTP_201_CREATED)
async def create_group_invitation(
    group_id: UUID,
    invitation_data: GroupInvitationCreate,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Create a group invitation"""
    group_service = GroupService(session)
    group = await group_service.get_group(group_id)
    if not group:
        raise HTTPException(status_code=404, detail="Group not found")
    
    # Check if user is the leader
    if group.leader_id != current_user.id:
        raise HTTPException(status_code=403, detail="Only group leader can send invitations")
    
    invitation_data.group_id = group_id
    return await group_service.create_invitation(invitation_data, current_user.id)

@router.post("/invitations/{invitation_id}/accept", response_model=GroupMembership, status_code=status.HTTP_201_CREATED)
async def accept_group_invitation(
    invitation_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Accept a group invitation"""
    group_service = GroupService(session)
    membership = await group_service.accept_invitation(invitation_id, current_user.id)
    if not membership:
        raise HTTPException(status_code=400, detail="Invalid invitation or already accepted")
    return membership

@router.delete("/invitations/{invitation_id}/decline", status_code=status.HTTP_204_NO_CONTENT)
async def decline_group_invitation(
    invitation_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Decline a group invitation"""
    group_service = GroupService(session)
    success = await group_service.decline_invitation(invitation_id, current_user.id)
    if not success:
        raise HTTPException(status_code=400, detail="Invalid invitation")

# Group Activities
@router.get("/{group_id}/activities", response_model=List[GroupActivity])
async def get_group_activities(
    group_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get group activities"""
    group_service = GroupService(session)

    # Check if user is a member
    if not await group_service.is_member(group_id, current_user.id):
        raise HTTPException(status_code=403, detail="Not authorized to view group activities")

    return await group_service.get_activities(group_id, skip=skip, limit=limit)

@router.post("/{group_id}/activities", response_model=GroupActivity, status_code=status.HTTP_201_CREATED)
async def create_group_activity(
    group_id: UUID,
    activity_data: GroupActivityCreate,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Create a group activity"""
    group_service = GroupService(session)

    # Check if user is a member
    if not await group_service.is_member(group_id, current_user.id):
        raise HTTPException(status_code=403, detail="Not authorized to create activities in this group")

    activity_data.group_id = group_id
    return await group_service.create_activity(activity_data, current_user.id)

# User's Groups
@router.get("/my/groups", response_model=List[Group])
async def get_my_groups(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get current user's groups"""
    group_service = GroupService(session)
    return await group_service.get_user_groups(current_user.id, skip=skip, limit=limit)

@router.get("/my/invitations", response_model=List[GroupInvitation])
async def get_my_invitations(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get current user's pending invitations"""
    group_service = GroupService(session)
    return await group_service.get_user_invitations(current_user.id, skip=skip, limit=limit)

# Group Analytics
@router.get("/{group_id}/analytics", response_model=dict)
async def get_group_analytics(
    group_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get group analytics (leader only)"""
    group_service = GroupService(session)
    group = await group_service.get_group(group_id)
    if not group:
        raise HTTPException(status_code=404, detail="Group not found")

    # Check if user is the leader
    if group.leader_id != current_user.id:
        raise HTTPException(status_code=403, detail="Only group leader can view analytics")

    return await group_service.get_group_analytics(group_id)
