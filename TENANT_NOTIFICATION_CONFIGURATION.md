# Tenant Notification Configuration System

## 🎯 **Overview**

The Arroyo University platform now supports **tenant-specific notification configurations**, allowing each tenant to configure their own notification channels (Email, SMS, Slack, Teams, Webhooks, etc.) with their own credentials and settings. This provides complete flexibility and isolation between tenants.

## ✨ **Key Features**

### **🔧 Configurable Notification Channels**
- **Email (SMTP)** - Custom SMTP servers with tenant credentials
- **SMS (Twilio)** - Tenant-specific Twilio accounts
- **Slack** - Tenant Slack workspaces with bot tokens
- **Microsoft Teams** - Tenant Teams channels via webhooks
- **Custom Webhooks** - Integration with external systems
- **Push Notifications** - Firebase/APNS with tenant keys

### **🔐 Security Features**
- **Encrypted Storage** - Sensitive credentials encrypted at rest
- **Masked Responses** - API responses mask sensitive data
- **Secure Testing** - Test configurations without exposing credentials
- **Access Control** - Role-based permissions for configuration management

### **⚙️ Management Features**
- **Enable/Disable Channels** - Toggle notification channels on/off
- **Priority Settings** - Configure channel priority and fallbacks
- **Rate Limiting** - Per-tenant rate limits for each channel
- **Usage Analytics** - Track notification delivery and failures
- **Configuration Templates** - Guided setup with examples

## 🏗️ **Architecture**

### **Database Models**

#### **TenantNotificationConfig**
Stores tenant-specific notification channel configurations:

```sql
CREATE TABLE tenant_notification_configs (
    config_id UUID PRIMARY KEY,
    tenant_id UUID REFERENCES tenants(tenant_id),
    channel_type VARCHAR(50) NOT NULL, -- email, sms, slack, teams, webhook, push
    is_enabled BOOLEAN DEFAULT FALSE,
    status VARCHAR(20) DEFAULT 'inactive', -- active, inactive, testing, error
    name VARCHAR(255) NOT NULL,
    description TEXT,
    priority INTEGER DEFAULT 5, -- 1=highest, 10=lowest
    config_data JSONB NOT NULL, -- Encrypted configuration data
    rate_limit_per_hour INTEGER,
    rate_limit_per_day INTEGER,
    max_retry_attempts INTEGER DEFAULT 3,
    retry_delay_seconds INTEGER DEFAULT 60,
    last_test_at TIMESTAMP,
    last_test_result VARCHAR(20),
    last_error TEXT,
    total_sent INTEGER DEFAULT 0,
    total_failed INTEGER DEFAULT 0,
    last_used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### **TenantNotificationRule**
Defines automatic notification rules:

```sql
CREATE TABLE tenant_notification_rules (
    rule_id UUID PRIMARY KEY,
    tenant_id UUID REFERENCES tenants(tenant_id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    is_enabled BOOLEAN DEFAULT TRUE,
    event_type VARCHAR(100) NOT NULL, -- course_enrollment, exam_graded, etc.
    conditions JSONB DEFAULT '{}',
    channels JSONB NOT NULL, -- Array of channel types
    template_name VARCHAR(255),
    delay_minutes INTEGER DEFAULT 0,
    max_per_user_per_day INTEGER,
    cooldown_minutes INTEGER,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### **API Endpoints**

#### **Configuration Management**
- `GET /api/v1/tenant/notifications/templates` - Get channel templates
- `GET /api/v1/tenant/notifications/configs` - List configurations
- `POST /api/v1/tenant/notifications/configs` - Create configuration
- `GET /api/v1/tenant/notifications/configs/{id}` - Get configuration
- `PUT /api/v1/tenant/notifications/configs/{id}` - Update configuration
- `DELETE /api/v1/tenant/notifications/configs/{id}` - Delete configuration
- `POST /api/v1/tenant/notifications/configs/{id}/test` - Test configuration
- `POST /api/v1/tenant/notifications/configs/{id}/toggle` - Enable/disable

#### **Monitoring & Analytics**
- `GET /api/v1/tenant/notifications/status` - Overall status
- `POST /api/v1/tenant/notifications/test-all` - Test all configurations
- `GET /api/v1/tenant/notifications/usage-stats` - Usage statistics

## 📋 **Configuration Examples**

### **Email (SMTP) Configuration**

```json
{
  "channel_type": "email",
  "name": "Company SMTP Server",
  "description": "Primary email notifications via company SMTP",
  "is_enabled": true,
  "priority": 1,
  "config_data": {
    "smtp_host": "smtp.company.com",
    "smtp_port": 587,
    "smtp_user": "<EMAIL>",
    "smtp_password": "secure-password",
    "from_email": "<EMAIL>",
    "from_name": "Company Learning Platform",
    "smtp_tls": true,
    "smtp_ssl": false
  },
  "rate_limit_per_hour": 1000,
  "rate_limit_per_day": 10000
}
```

### **Slack Configuration**

```json
{
  "channel_type": "slack",
  "name": "Company Slack Workspace",
  "description": "Notifications to company Slack channels",
  "is_enabled": true,
  "priority": 2,
  "config_data": {
    "bot_token": "xoxb-your-bot-token",
    "default_channel": "#learning-notifications",
    "signing_secret": "your-signing-secret"
  },
  "rate_limit_per_hour": 100
}
```

### **SMS (Twilio) Configuration**

```json
{
  "channel_type": "sms",
  "name": "Twilio SMS Service",
  "description": "SMS notifications via Twilio",
  "is_enabled": false,
  "priority": 3,
  "config_data": {
    "account_sid": "ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "auth_token": "your-auth-token",
    "phone_number": "+**********"
  },
  "rate_limit_per_hour": 50,
  "rate_limit_per_day": 200
}
```

### **Custom Webhook Configuration**

```json
{
  "channel_type": "webhook",
  "name": "External System Integration",
  "description": "Send notifications to external system",
  "is_enabled": true,
  "priority": 4,
  "config_data": {
    "url": "https://external-system.com/api/notifications",
    "method": "POST",
    "headers": {
      "Content-Type": "application/json",
      "Authorization": "Bearer your-api-token"
    },
    "secret": "webhook-verification-secret"
  },
  "rate_limit_per_hour": 500
}
```

## 🔧 **Implementation Guide**

### **1. Setting Up Notification Channels**

#### **Step 1: Get Available Templates**
```bash
GET /api/v1/tenant/notifications/templates
```

This returns configuration templates for all supported channels with:
- Required fields
- Optional fields
- Field descriptions
- Example configurations

#### **Step 2: Create Configuration**
```bash
POST /api/v1/tenant/notifications/configs
Content-Type: application/json

{
  "channel_type": "email",
  "name": "Primary Email",
  "description": "Main email notifications",
  "is_enabled": false,
  "config_data": {
    "smtp_host": "smtp.gmail.com",
    "smtp_port": 587,
    "smtp_user": "<EMAIL>",
    "smtp_password": "your-app-password",
    "from_email": "<EMAIL>",
    "from_name": "Your Organization",
    "smtp_tls": true
  }
}
```

#### **Step 3: Test Configuration**
```bash
POST /api/v1/tenant/notifications/configs/{config_id}/test
```

#### **Step 4: Enable Configuration**
```bash
POST /api/v1/tenant/notifications/configs/{config_id}/toggle
```

### **2. Managing Multiple Channels**

Tenants can configure multiple channels of the same type (e.g., multiple email servers) with different priorities:

1. **Primary Email Server** (Priority 1)
2. **Backup Email Server** (Priority 2)
3. **Slack Notifications** (Priority 3)
4. **SMS Alerts** (Priority 4)

### **3. Monitoring and Maintenance**

#### **Check Overall Status**
```bash
GET /api/v1/tenant/notifications/status
```

#### **Test All Configurations**
```bash
POST /api/v1/tenant/notifications/test-all
```

#### **View Usage Statistics**
```bash
GET /api/v1/tenant/notifications/usage-stats?days=30
```

## 🔐 **Security Considerations**

### **Data Encryption**
- Sensitive configuration data is encrypted using Fernet encryption
- Encryption keys are stored separately from configuration data
- API responses mask sensitive fields (passwords, tokens, etc.)

### **Access Control**
- `notification_view` permission required to view configurations
- `notification_manage` permission required to create/update/delete configurations
- Tenant isolation ensures tenants can only access their own configurations

### **Testing Security**
- Test endpoints don't expose actual configuration data
- Failed tests log errors without revealing sensitive information
- Test results include success/failure status and generic error messages

## 🚀 **Benefits**

### **For Tenants**
- **Complete Control** - Configure their own notification channels
- **Flexibility** - Use their existing communication tools (Slack, Teams, etc.)
- **Security** - Keep their credentials within their control
- **Customization** - Set up notifications that match their workflow

### **For Platform**
- **Scalability** - No need to manage global notification credentials
- **Compliance** - Tenants handle their own data sovereignty requirements
- **Reliability** - Tenant-specific rate limits and fallbacks
- **Maintenance** - Reduced support burden for notification issues

## 📊 **Usage Analytics**

The system tracks:
- **Total notifications sent** per channel
- **Failure rates** and error types
- **Last usage timestamps**
- **Rate limit utilization**
- **Configuration test results**

This data helps tenants optimize their notification setup and troubleshoot issues.

## 🔄 **Migration from Global Configuration**

Existing tenants using global notification settings can:

1. **Continue using global settings** (backward compatibility maintained)
2. **Gradually migrate** to tenant-specific configurations
3. **Use hybrid approach** with some channels tenant-specific and others global

The system supports fallback to global configuration when tenant-specific configuration is not available.
