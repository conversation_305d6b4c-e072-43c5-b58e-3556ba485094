"""
Forums Router
Handles forum-related API endpoints including categories, posts, replies, and moderation
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlmodel import Session
from typing import List, Optional
from uuid import UUID

from ..core.database import get_session
from ..core.security import get_current_user, require_permissions
from ..models.user_models import User
from ..models.forum_models import (
    ForumCategory, ForumCategoryCreate, ForumCategoryUpdate,
    ForumPost, ForumPostCreate, ForumPostUpdate,
    ForumReply, ForumReplyCreate, ForumReplyUpdate,
    ForumReport, ForumReportCreate
)
from ..services.forum_service import ForumService

router = APIRouter()

# Forum Categories
@router.get("/categories", response_model=List[ForumCategory])
async def get_forum_categories(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    tenant_id: Optional[UUID] = None,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get all forum categories"""
    forum_service = ForumService(session)
    return await forum_service.get_categories(
        tenant_id=tenant_id or current_user.tenant_id,
        skip=skip,
        limit=limit
    )

@router.post("/categories", response_model=ForumCategory, status_code=status.HTTP_201_CREATED)
async def create_forum_category(
    category_data: ForumCategoryCreate,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["forum_manage"]))
):
    """Create a new forum category"""
    forum_service = ForumService(session)
    return await forum_service.create_category(category_data, current_user.id)

@router.get("/categories/{category_id}", response_model=ForumCategory)
async def get_forum_category(
    category_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get a specific forum category"""
    forum_service = ForumService(session)
    category = await forum_service.get_category(category_id)
    if not category:
        raise HTTPException(status_code=404, detail="Forum category not found")
    return category

@router.put("/categories/{category_id}", response_model=ForumCategory)
async def update_forum_category(
    category_id: UUID,
    category_data: ForumCategoryUpdate,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["forum_manage"]))
):
    """Update a forum category"""
    forum_service = ForumService(session)
    category = await forum_service.update_category(category_id, category_data)
    if not category:
        raise HTTPException(status_code=404, detail="Forum category not found")
    return category

@router.delete("/categories/{category_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_forum_category(
    category_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["forum_manage"]))
):
    """Delete a forum category"""
    forum_service = ForumService(session)
    success = await forum_service.delete_category(category_id)
    if not success:
        raise HTTPException(status_code=404, detail="Forum category not found")

# Forum Posts
@router.get("/posts", response_model=List[ForumPost])
async def get_forum_posts(
    category_id: Optional[UUID] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: Optional[str] = None,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get forum posts with optional filtering"""
    forum_service = ForumService(session)
    return await forum_service.get_posts(
        category_id=category_id,
        tenant_id=current_user.tenant_id,
        skip=skip,
        limit=limit,
        search=search
    )

@router.post("/posts", response_model=ForumPost, status_code=status.HTTP_201_CREATED)
async def create_forum_post(
    post_data: ForumPostCreate,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["forum_post"]))
):
    """Create a new forum post"""
    forum_service = ForumService(session)
    return await forum_service.create_post(post_data, current_user.id)

@router.get("/posts/{post_id}", response_model=ForumPost)
async def get_forum_post(
    post_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get a specific forum post"""
    forum_service = ForumService(session)
    post = await forum_service.get_post(post_id)
    if not post:
        raise HTTPException(status_code=404, detail="Forum post not found")
    return post

@router.put("/posts/{post_id}", response_model=ForumPost)
async def update_forum_post(
    post_id: UUID,
    post_data: ForumPostUpdate,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Update a forum post"""
    forum_service = ForumService(session)
    post = await forum_service.get_post(post_id)
    if not post:
        raise HTTPException(status_code=404, detail="Forum post not found")
    
    # Check if user owns the post or has moderation permissions
    if post.author_id != current_user.id and not current_user.has_permission("forum_moderate"):
        raise HTTPException(status_code=403, detail="Not authorized to edit this post")
    
    updated_post = await forum_service.update_post(post_id, post_data)
    return updated_post

@router.delete("/posts/{post_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_forum_post(
    post_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Delete a forum post"""
    forum_service = ForumService(session)
    post = await forum_service.get_post(post_id)
    if not post:
        raise HTTPException(status_code=404, detail="Forum post not found")
    
    # Check if user owns the post or has moderation permissions
    if post.author_id != current_user.id and not current_user.has_permission("forum_moderate"):
        raise HTTPException(status_code=403, detail="Not authorized to delete this post")
    
    success = await forum_service.delete_post(post_id)
    if not success:
        raise HTTPException(status_code=404, detail="Forum post not found")

# Forum Replies
@router.get("/posts/{post_id}/replies", response_model=List[ForumReply])
async def get_forum_replies(
    post_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get replies for a forum post"""
    forum_service = ForumService(session)
    return await forum_service.get_replies(post_id, skip=skip, limit=limit)

@router.post("/posts/{post_id}/replies", response_model=ForumReply, status_code=status.HTTP_201_CREATED)
async def create_forum_reply(
    post_id: UUID,
    reply_data: ForumReplyCreate,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["forum_reply"]))
):
    """Create a reply to a forum post"""
    forum_service = ForumService(session)
    reply_data.post_id = post_id
    return await forum_service.create_reply(reply_data, current_user.id)

@router.put("/replies/{reply_id}", response_model=ForumReply)
async def update_forum_reply(
    reply_id: UUID,
    reply_data: ForumReplyUpdate,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Update a forum reply"""
    forum_service = ForumService(session)
    reply = await forum_service.get_reply(reply_id)
    if not reply:
        raise HTTPException(status_code=404, detail="Forum reply not found")

    # Check if user owns the reply or has moderation permissions
    if reply.author_id != current_user.id and not current_user.has_permission("forum_moderate"):
        raise HTTPException(status_code=403, detail="Not authorized to edit this reply")

    updated_reply = await forum_service.update_reply(reply_id, reply_data)
    return updated_reply

@router.delete("/replies/{reply_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_forum_reply(
    reply_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Delete a forum reply"""
    forum_service = ForumService(session)
    reply = await forum_service.get_reply(reply_id)
    if not reply:
        raise HTTPException(status_code=404, detail="Forum reply not found")

    # Check if user owns the reply or has moderation permissions
    if reply.author_id != current_user.id and not current_user.has_permission("forum_moderate"):
        raise HTTPException(status_code=403, detail="Not authorized to delete this reply")

    success = await forum_service.delete_reply(reply_id)
    if not success:
        raise HTTPException(status_code=404, detail="Forum reply not found")

# Forum Likes
@router.post("/posts/{post_id}/like", status_code=status.HTTP_204_NO_CONTENT)
async def toggle_post_like(
    post_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Toggle like on a forum post"""
    forum_service = ForumService(session)
    await forum_service.toggle_post_like(post_id, current_user.id)

@router.post("/replies/{reply_id}/like", status_code=status.HTTP_204_NO_CONTENT)
async def toggle_reply_like(
    reply_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Toggle like on a forum reply"""
    forum_service = ForumService(session)
    await forum_service.toggle_reply_like(reply_id, current_user.id)

# Forum Reports
@router.post("/reports", response_model=ForumReport, status_code=status.HTTP_201_CREATED)
async def create_forum_report(
    report_data: ForumReportCreate,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Report inappropriate content"""
    forum_service = ForumService(session)
    return await forum_service.create_report(report_data, current_user.id)

@router.get("/reports", response_model=List[ForumReport])
async def get_forum_reports(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status_filter: Optional[str] = None,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["forum_moderate"]))
):
    """Get forum reports (moderators only)"""
    forum_service = ForumService(session)
    return await forum_service.get_reports(
        tenant_id=current_user.tenant_id,
        skip=skip,
        limit=limit,
        status_filter=status_filter
    )

@router.put("/reports/{report_id}/resolve", response_model=ForumReport)
async def resolve_forum_report(
    report_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["forum_moderate"]))
):
    """Resolve a forum report"""
    forum_service = ForumService(session)
    report = await forum_service.resolve_report(report_id, current_user.id)
    if not report:
        raise HTTPException(status_code=404, detail="Forum report not found")
    return report

# Forum Search
@router.get("/search", response_model=dict)
async def search_forums(
    query: str = Query(..., min_length=3),
    category_id: Optional[UUID] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Search forum posts and replies"""
    forum_service = ForumService(session)
    return await forum_service.search_content(
        query=query,
        tenant_id=current_user.tenant_id,
        category_id=category_id,
        skip=skip,
        limit=limit
    )
