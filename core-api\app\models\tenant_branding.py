"""
Tenant branding configuration models
"""

from datetime import datetime
from typing import Optional, Dict, Any
from uuid import UUID
from enum import Enum

from sqlmodel import SQLModel, Field

from .base import TimestampMixin, TenantMixin


class HeaderStyle(str, Enum):
    """Header style options"""
    DEFAULT = "default"
    MINIMAL = "minimal"
    BOLD = "bold"


class SidebarStyle(str, Enum):
    """Sidebar style options"""
    DEFAULT = "default"
    COMPACT = "compact"
    EXPANDED = "expanded"


class CardStyle(str, Enum):
    """Card style options"""
    DEFAULT = "default"
    ELEVATED = "elevated"
    FLAT = "flat"


class ButtonStyle(str, Enum):
    """Button style options"""
    DEFAULT = "default"
    ROUNDED = "rounded"
    SQUARE = "square"


class TenantBrandingBase(SQLModel):
    """Base tenant branding configuration model"""
    # Organization Information
    organization_name: str = Field(max_length=255)
    display_name: Optional[str] = Field(default=None, max_length=255)
    description: Optional[str] = Field(default=None, max_length=1000)
    website: Optional[str] = Field(default=None, max_length=255)
    subdomain: str = Field(max_length=100)
    custom_domain: Optional[str] = Field(default=None, max_length=255)
    
    # Brand Colors
    primary_color: str = Field(default="#3B82F6", max_length=7)  # Hex color
    secondary_color: str = Field(default="#10B981", max_length=7)
    accent_color: str = Field(default="#F59E0B", max_length=7)
    background_color: str = Field(default="#FFFFFF", max_length=7)
    text_color: str = Field(default="#1F2937", max_length=7)
    
    # Logo and Images
    logo_url: Optional[str] = Field(default=None, max_length=500)
    logo_light_url: Optional[str] = Field(default=None, max_length=500)
    logo_dark_url: Optional[str] = Field(default=None, max_length=500)
    favicon_url: Optional[str] = Field(default=None, max_length=500)
    login_background_url: Optional[str] = Field(default=None, max_length=500)
    
    # Typography and Styling
    font_family: str = Field(default="Inter", max_length=100)
    border_radius: str = Field(default="8px", max_length=20)
    header_style: HeaderStyle = Field(default=HeaderStyle.DEFAULT)
    sidebar_style: SidebarStyle = Field(default=SidebarStyle.DEFAULT)
    card_style: CardStyle = Field(default=CardStyle.DEFAULT)
    button_style: ButtonStyle = Field(default=ButtonStyle.DEFAULT)
    
    # Advanced Features
    custom_css: Optional[str] = Field(default=None)
    custom_js: Optional[str] = Field(default=None)
    custom_footer: Optional[str] = Field(default=None)
    hide_arroyo_branding: bool = Field(default=False)
    enable_white_label: bool = Field(default=False)


class TenantBranding(TenantBrandingBase, TenantMixin, TimestampMixin, table=True):
    """Tenant branding configuration table model"""
    __tablename__ = "tenant_branding"
    
    branding_id: UUID = Field(primary_key=True)
    
    # Additional metadata
    is_active: bool = Field(default=True)
    version: int = Field(default=1)  # For versioning branding changes
    
    # File storage metadata
    logo_file_size: Optional[int] = Field(default=None)
    favicon_file_size: Optional[int] = Field(default=None)
    background_file_size: Optional[int] = Field(default=None)
    
    # Usage tracking
    last_applied_at: Optional[datetime] = None
    applied_by: Optional[UUID] = Field(foreign_key="users.user_id", default=None)


class TenantBrandingCreate(TenantBrandingBase):
    """Tenant branding creation model"""
    pass


class TenantBrandingUpdate(SQLModel):
    """Tenant branding update model"""
    organization_name: Optional[str] = None
    display_name: Optional[str] = None
    description: Optional[str] = None
    website: Optional[str] = None
    subdomain: Optional[str] = None
    custom_domain: Optional[str] = None
    
    primary_color: Optional[str] = None
    secondary_color: Optional[str] = None
    accent_color: Optional[str] = None
    background_color: Optional[str] = None
    text_color: Optional[str] = None
    
    logo_url: Optional[str] = None
    logo_light_url: Optional[str] = None
    logo_dark_url: Optional[str] = None
    favicon_url: Optional[str] = None
    login_background_url: Optional[str] = None
    
    font_family: Optional[str] = None
    border_radius: Optional[str] = None
    header_style: Optional[HeaderStyle] = None
    sidebar_style: Optional[SidebarStyle] = None
    card_style: Optional[CardStyle] = None
    button_style: Optional[ButtonStyle] = None
    
    custom_css: Optional[str] = None
    custom_js: Optional[str] = None
    custom_footer: Optional[str] = None
    hide_arroyo_branding: Optional[bool] = None
    enable_white_label: Optional[bool] = None


class TenantBrandingResponse(TenantBrandingBase):
    """Tenant branding response model"""
    branding_id: UUID
    tenant_id: UUID
    is_active: bool
    version: int
    logo_file_size: Optional[int]
    favicon_file_size: Optional[int]
    background_file_size: Optional[int]
    last_applied_at: Optional[datetime]
    applied_by: Optional[UUID]
    created_at: datetime
    updated_at: datetime


class TenantBrandingHistory(SQLModel, table=True):
    """Tenant branding history for tracking changes"""
    __tablename__ = "tenant_branding_history"
    
    history_id: UUID = Field(primary_key=True)
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)
    branding_id: UUID = Field(foreign_key="tenant_branding.branding_id")
    
    # Snapshot of branding data at time of change
    branding_data: Dict[str, Any] = Field(sa_column_kwargs={"type_": "JSONB"})
    
    # Change metadata
    changed_by: UUID = Field(foreign_key="users.user_id")
    change_reason: Optional[str] = Field(default=None, max_length=500)
    version: int
    
    created_at: datetime = Field(default_factory=datetime.utcnow)


class SubdomainAvailability(SQLModel):
    """Model for checking subdomain availability"""
    subdomain: str
    is_available: bool
    suggestions: Optional[list] = Field(default_factory=list)


class BrandingValidation(SQLModel):
    """Model for branding validation results"""
    is_valid: bool
    errors: list = Field(default_factory=list)
    warnings: list = Field(default_factory=list)
    
    # Specific validation results
    subdomain_valid: bool = True
    colors_valid: bool = True
    files_valid: bool = True
    css_valid: bool = True
    js_valid: bool = True


class BrandingPreview(SQLModel):
    """Model for branding preview data"""
    preview_url: str
    expires_at: datetime
    preview_token: str


# Default branding configuration
DEFAULT_BRANDING = {
    "organization_name": "Mi Organización",
    "display_name": "Mi Organización - Plataforma de Aprendizaje",
    "description": "Plataforma de aprendizaje y evaluación para nuestra organización",
    "website": "",
    "subdomain": "",
    "custom_domain": "",
    "primary_color": "#3B82F6",
    "secondary_color": "#10B981",
    "accent_color": "#F59E0B",
    "background_color": "#FFFFFF",
    "text_color": "#1F2937",
    "logo_url": None,
    "logo_light_url": None,
    "logo_dark_url": None,
    "favicon_url": None,
    "login_background_url": None,
    "font_family": "Inter",
    "border_radius": "8px",
    "header_style": "default",
    "sidebar_style": "default",
    "card_style": "default",
    "button_style": "default",
    "custom_css": "",
    "custom_js": "",
    "custom_footer": "",
    "hide_arroyo_branding": False,
    "enable_white_label": False
}


# CSS Variables template for dynamic theming
CSS_VARIABLES_TEMPLATE = """
:root {{
  --primary-color: {primary_color};
  --secondary-color: {secondary_color};
  --accent-color: {accent_color};
  --background-color: {background_color};
  --text-color: {text_color};
  --font-family: {font_family};
  --border-radius: {border_radius};
}}

/* Custom CSS */
{custom_css}
"""
