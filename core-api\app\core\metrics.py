"""
Prometheus metrics configuration for Arroyo University Core API
"""

from prometheus_client import CollectorRegistry, Counter, Histogram, Gauge, Info
import time
import psutil
import os

# Create a custom registry for our metrics
_registry = CollectorRegistry()

# Application info
app_info = Info(
    'arroyo_university_app_info',
    'Application information',
    registry=_registry
)

# HTTP request metrics
http_requests_total = Counter(
    'arroyo_university_http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status_code'],
    registry=_registry
)

http_request_duration_seconds = Histogram(
    'arroyo_university_http_request_duration_seconds',
    'HTTP request duration in seconds',
    ['method', 'endpoint'],
    registry=_registry
)

# Database metrics
database_connections_active = Gauge(
    'arroyo_university_database_connections_active',
    'Active database connections',
    registry=_registry
)

database_query_duration_seconds = Histogram(
    'arroyo_university_database_query_duration_seconds',
    'Database query duration in seconds',
    ['operation'],
    registry=_registry
)

# AI service metrics
ai_requests_total = Counter(
    'arroyo_university_ai_requests_total',
    'Total AI service requests',
    ['service_type', 'status'],
    registry=_registry
)

ai_processing_duration_seconds = Histogram(
    'arroyo_university_ai_processing_duration_seconds',
    'AI processing duration in seconds',
    ['service_type'],
    registry=_registry
)

# User activity metrics
active_users = Gauge(
    'arroyo_university_active_users',
    'Number of active users',
    ['tenant_id'],
    registry=_registry
)

exam_submissions_total = Counter(
    'arroyo_university_exam_submissions_total',
    'Total exam submissions',
    ['tenant_id', 'exam_type'],
    registry=_registry
)

# System metrics
system_memory_usage_bytes = Gauge(
    'arroyo_university_system_memory_usage_bytes',
    'System memory usage in bytes',
    registry=_registry
)

system_cpu_usage_percent = Gauge(
    'arroyo_university_system_cpu_usage_percent',
    'System CPU usage percentage',
    registry=_registry
)

# Cache metrics
cache_hits_total = Counter(
    'arroyo_university_cache_hits_total',
    'Total cache hits',
    ['cache_type'],
    registry=_registry
)

cache_misses_total = Counter(
    'arroyo_university_cache_misses_total',
    'Total cache misses',
    ['cache_type'],
    registry=_registry
)

# File upload metrics
file_uploads_total = Counter(
    'arroyo_university_file_uploads_total',
    'Total file uploads',
    ['file_type', 'status'],
    registry=_registry
)

file_upload_size_bytes = Histogram(
    'arroyo_university_file_upload_size_bytes',
    'File upload size in bytes',
    ['file_type'],
    registry=_registry
)


def get_metrics_registry():
    """Get the metrics registry"""
    return _registry


def initialize_metrics():
    """Initialize application metrics with basic info"""
    app_info.info({
        'version': os.getenv('APP_VERSION', '1.0.0'),
        'environment': os.getenv('ENVIRONMENT', 'development'),
        'service': 'core-api'
    })


def update_system_metrics():
    """Update system-level metrics"""
    try:
        # Memory usage
        memory = psutil.virtual_memory()
        system_memory_usage_bytes.set(memory.used)
        
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        system_cpu_usage_percent.set(cpu_percent)
    except Exception:
        # If psutil is not available or fails, skip system metrics
        pass


def record_http_request(method: str, endpoint: str, status_code: int, duration: float):
    """Record HTTP request metrics"""
    http_requests_total.labels(
        method=method,
        endpoint=endpoint,
        status_code=str(status_code)
    ).inc()
    
    http_request_duration_seconds.labels(
        method=method,
        endpoint=endpoint
    ).observe(duration)


def record_database_query(operation: str, duration: float):
    """Record database query metrics"""
    database_query_duration_seconds.labels(operation=operation).observe(duration)


def record_ai_request(service_type: str, status: str, duration: float):
    """Record AI service request metrics"""
    ai_requests_total.labels(service_type=service_type, status=status).inc()
    ai_processing_duration_seconds.labels(service_type=service_type).observe(duration)


def record_exam_submission(tenant_id: str, exam_type: str):
    """Record exam submission"""
    exam_submissions_total.labels(tenant_id=tenant_id, exam_type=exam_type).inc()


def record_cache_hit(cache_type: str):
    """Record cache hit"""
    cache_hits_total.labels(cache_type=cache_type).inc()


def record_cache_miss(cache_type: str):
    """Record cache miss"""
    cache_misses_total.labels(cache_type=cache_type).inc()


def record_file_upload(file_type: str, status: str, size_bytes: int):
    """Record file upload metrics"""
    file_uploads_total.labels(file_type=file_type, status=status).inc()
    file_upload_size_bytes.labels(file_type=file_type).observe(size_bytes)


def set_active_users(tenant_id: str, count: int):
    """Set active users count for a tenant"""
    active_users.labels(tenant_id=tenant_id).set(count)


def set_database_connections(count: int):
    """Set active database connections count"""
    database_connections_active.set(count)


# Initialize metrics on module import
initialize_metrics()
