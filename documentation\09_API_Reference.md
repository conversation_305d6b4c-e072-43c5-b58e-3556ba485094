# API Reference - Arroyo University

## Introducción

Este documento proporciona la referencia completa de la API REST de Arroyo University. Todas las APIs siguen estándares RESTful, utilizan JSON para intercambio de datos y implementan autenticación JWT. La documentación incluye endpoints, parámetros, respuestas y ejemplos de uso.

---

## 1. Convenciones Generales

### 1.1 Base URL y Versionado
```
Base URL: https://api.arroyo.app/v1
Tenant-specific: https://{tenant}.arroyo.app/api/v1
```

### 1.2 Autenticación
| Tipo | Descripción | Header |
|------|-------------|--------|
| 🔒 **JWT** | Token de acceso requerido | `Authorization: Bearer {token}` |
| 🔑 **HMAC** | Firma para webhooks | `X-Signature: sha256={signature}` |
| 🌐 **Público** | Sin autenticación requerida | N/A |

### 1.3 Códigos de Respuesta
| Código | Significado | Uso |
|--------|-------------|-----|
| `200` | OK | Operación exitosa |
| `201` | Created | Recurso creado exitosamente |
| `204` | No Content | Operación exitosa sin contenido |
| `400` | Bad Request | Error en parámetros de entrada |
| `401` | Unauthorized | Token inválido o expirado |
| `403` | Forbidden | Sin permisos para la operación |
| `404` | Not Found | Recurso no encontrado |
| `409` | Conflict | Conflicto con estado actual |
| `422` | Unprocessable Entity | Error de validación |
| `429` | Too Many Requests | Rate limit excedido |
| `500` | Internal Server Error | Error interno del servidor |

### 1.4 Formato de Errores (RFC 7807)
```json
{
  "type": "https://api.arroyo.app/errors/validation-error",
  "title": "Validation Error",
  "status": 422,
  "detail": "The request body contains invalid data",
  "instance": "/v1/courses",
  "errors": [
    {
      "field": "title",
      "code": "required",
      "message": "Title is required"
    }
  ]
}
```

---

## 2. Autenticación y Seguridad

### 2.1 Login
```http
POST /auth/login
Content-Type: application/json
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "mfa_code": "123456"  // Opcional si MFA habilitado
}
```

**Response (200):**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 900,
  "user": {
    "user_id": "123e4567-e89b-12d3-a456-426614174000",
    "email": "<EMAIL>",
    "roles": ["content_creator"],
    "tenant_id": "123e4567-e89b-12d3-a456-426614174001"
  }
}
```

### 2.2 Refresh Token
```http
POST /auth/refresh
Content-Type: application/json
```

**Request Body:**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 2.3 Logout
```http
POST /auth/logout 🔒
Authorization: Bearer {access_token}
```

---

## 3. Gestión de Tenants

### 3.1 Crear Tenant
```http
POST /tenants 🔒
Authorization: Bearer {sysadmin_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Acme Corporation",
  "description": "Corporate training platform",
  "plan": "premium",
  "admin_email": "<EMAIL>",
  "settings": {
    "branding": {
      "logo_url": "https://acme.com/logo.png",
      "primary_color": "#007bff"
    },
    "features": {
      "sso_enabled": true,
      "ai_generation_limit": 1000
    }
  }
}
```

**Response (201):**
```json
{
  "tenant_id": "123e4567-e89b-12d3-a456-426614174001",
  "name": "Acme Corporation",
  "subdomain": "acme",
  "status": "provisioning",
  "created_at": "2024-01-15T10:30:00Z",
  "onboarding_url": "https://acme.arroyo.app/onboarding?token=..."
}
```

### 3.2 Obtener Tenant
```http
GET /tenants/{tenant_id} 🔒
Authorization: Bearer {token}
```

### 3.3 Actualizar Configuración
```http
PATCH /tenants/{tenant_id} 🔒
Authorization: Bearer {admin_tenant_token}
Content-Type: application/json
```

---

## 4. Gestión de Usuarios

### 4.1 Crear Usuario
```http
POST /users 🔒
Authorization: Bearer {admin_tenant_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "newuser",
  "roles": ["student"],
  "groups": ["group_123"],
  "temporary": false,
  "expires_at": null,
  "send_invitation": true
}
```

**Response (201):**
```json
{
  "user_id": "123e4567-e89b-12d3-a456-426614174002",
  "email": "<EMAIL>",
  "username": "newuser",
  "verified": false,
  "invitation_sent": true,
  "invitation_expires_at": "2024-01-16T10:30:00Z",
  "created_at": "2024-01-15T10:30:00Z"
}
```

### 4.2 Listar Usuarios
```http
GET /users 🔒
Authorization: Bearer {token}
```

**Query Parameters:**
- `page`: Número de página (default: 1)
- `limit`: Elementos por página (default: 20, max: 100)
- `search`: Búsqueda por email/username
- `role`: Filtrar por rol
- `status`: active, inactive, pending

**Response (200):**
```json
{
  "users": [
    {
      "user_id": "123e4567-e89b-12d3-a456-426614174002",
      "email": "<EMAIL>",
      "username": "user",
      "roles": ["student"],
      "last_login": "2024-01-15T09:15:00Z",
      "status": "active"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "pages": 8
  }
}
```

### 4.3 Actualizar Usuario
```http
PATCH /users/{user_id} 🔒
Authorization: Bearer {token}
Content-Type: application/json
```

---

## 5. Gestión de Cursos

### 5.1 Crear Curso
```http
POST /courses 🔒
Authorization: Bearer {content_creator_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "title": "Advanced English Communication",
  "description": "Comprehensive course for business English",
  "metadata": {
    "difficulty": "B2",
    "estimated_hours": 40,
    "prerequisites": ["basic_english"],
    "tags": ["business", "communication", "english"]
  }
}
```

**Response (201):**
```json
{
  "course_id": "123e4567-e89b-12d3-a456-426614174003",
  "title": "Advanced English Communication",
  "status": "draft",
  "created_at": "2024-01-15T10:30:00Z",
  "version": 1
}
```

### 5.2 Subir Contenido Multimedia
```http
POST /courses/{course_id}/items 🔒
Authorization: Bearer {content_creator_token}
Content-Type: multipart/form-data
```

**Form Data:**
- `file`: Archivo multimedia (max 200MB)
- `item_type`: video, audio, pdf, image
- `title`: Título del contenido
- `description`: Descripción opcional

### 5.3 Publicar Curso
```http
PATCH /courses/{course_id} 🔒
Authorization: Bearer {content_creator_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "status": "published"
}
```

### 5.4 Obtener Cursos Administrados
```http
GET /courses/administered 🔒
Authorization: Bearer {user_token}
```

**Response:**
```json
{
  "courses": [
    {
      "course_id": "course_123",
      "title": "DevOps Fundamentals",
      "description": "Learn the basics of DevOps practices",
      "user_role": "creator",
      "student_count": 156,
      "module_count": 8,
      "likes": 89,
      "dislikes": 3,
      "created_at": "2023-11-01T10:00:00Z"
    }
  ]
}
```

### 5.5 Gestionar Colaboradores
```http
POST /courses/{course_id}/collaborators 🔒
Authorization: Bearer {course_creator_token}
Content-Type: application/json

{
  "user_id": "user_456",
  "role": "collaborator",
  "permissions": {
    "can_edit_content": true,
    "can_grade_exams": true,
    "can_manage_students": false
  }
}
```

### 5.6 Valorar Curso
```http
POST /courses/{course_id}/rating 🔒
Authorization: Bearer {user_token}
Content-Type: application/json

{
  "rating_type": "like"
}
```

### 5.7 Reportar Curso
```http
POST /courses/{course_id}/reports 🔒
Authorization: Bearer {user_token}
Content-Type: application/json

{
  "reason": "Contenido inapropiado",
  "comment": "El curso contiene información incorrecta sobre las mejores prácticas de seguridad."
}
```

### 5.8 Obtener Foro del Curso
```http
GET /courses/{course_id}/forum 🔒
Authorization: Bearer {user_token}
```

**Response:**
```json
{
  "posts": [
    {
      "post_id": "post_123",
      "title": "¿Cuál es la diferencia entre CI y CD?",
      "content": "Estoy en el módulo 3 y me surge esta duda...",
      "category": "technical",
      "user": {
        "user_id": "user_456",
        "name": "Carlos Mendoza",
        "is_expert": false
      },
      "is_highlighted": false,
      "likes_count": 8,
      "replies_count": 5,
      "created_at": "2023-11-20T10:30:00Z"
    }
  ],
  "highlighted_posts": [
    {
      "post_id": "post_789",
      "title": "Bienvenidos al curso",
      "content": "Como experta en DevOps...",
      "user": {
        "user_id": "expert_123",
        "name": "Dr. María González",
        "is_expert": true,
        "expert_area": "DevOps"
      },
      "is_highlighted": true,
      "highlighted_by": "expert_123",
      "likes_count": 47,
      "replies_count": 12,
      "created_at": "2023-11-18T14:00:00Z"
    }
  ]
}
```

### 5.9 Crear Post en Foro
```http
POST /courses/{course_id}/forum/posts 🔒
Authorization: Bearer {user_token}
Content-Type: application/json

{
  "title": "Pregunta sobre Docker",
  "content": "¿Cuáles son las mejores prácticas para optimizar imágenes Docker?",
  "category": "technical"
}
```

### 5.10 Destacar Post (Solo Expertos)
```http
POST /courses/{course_id}/forum/posts/{post_id}/highlight 🔒
Authorization: Bearer {expert_token}
```

---

## 5.11 Gestión de Expertos

### 5.11.1 Asignar Experto (Solo Admin Tenant)
```http
POST /users/{user_id}/expert-areas 🔒
Authorization: Bearer {admin_tenant_token}
Content-Type: application/json

{
  "area_name": "DevOps"
}
```

### 5.11.2 Crear Review de Experto
```http
POST /courses/{course_id}/expert-review 🔒
Authorization: Bearer {expert_token}
Content-Type: application/json

{
  "rating": "excelente",
  "title": "Un curso excepcional que cubre todos los aspectos fundamentales",
  "review_text": "Como profesional con más de 10 años en DevOps, puedo afirmar que este curso presenta una excelente progresión...",
  "expert_area": "DevOps"
}
```

**Response:**
```json
{
  "review_id": "review_123",
  "status": "pending_verification",
  "message": "Review creado exitosamente. Pendiente de verificación por Admin Tenant."
}
```

### 5.11.3 Verificar Review de Experto (Solo Admin Tenant)
```http
POST /expert-reviews/{review_id}/verify 🔒
Authorization: Bearer {admin_tenant_token}
Content-Type: application/json

{
  "is_verified": true
}
```

### 5.11.4 Obtener Reviews de Expertos para Curso
```http
GET /courses/{course_id}/expert-reviews
```

**Response:**
```json
{
  "reviews": [
    {
      "review_id": "review_123",
      "expert": {
        "name": "Dr. María González",
        "expert_area": "DevOps"
      },
      "rating": "excelente",
      "title": "Un curso excepcional que cubre todos los aspectos fundamentales",
      "review_text": "Como profesional con más de 10 años en DevOps...",
      "is_verified": true,
      "verified_at": "2023-11-20T15:30:00Z",
      "created_at": "2023-11-20T10:00:00Z"
    }
  ]
}
```

---

## 5.12 Career Paths

### 5.12.1 Obtener Career Paths Marketplace
```http
GET /career-paths/marketplace 🔒
Authorization: Bearer {user_token}
```

**Response:**
```json
{
  "featured_paths": [
    {
      "path_id": "path_123",
      "name": "Sysadmin → DevOps Engineer",
      "description": "Transición completa desde administrador de sistemas...",
      "from_position": "System Administrator",
      "to_position": "DevOps Engineer",
      "estimated_duration_months": 10,
      "difficulty_level": "intermediate",
      "rating_avg": 4.8,
      "users_count": 1234,
      "skills_count": 15,
      "preview_diagram": {
        "start_skill": "Linux Administration",
        "end_skill": "Kubernetes"
      }
    }
  ],
  "user_active_paths": [
    {
      "path_id": "path_456",
      "name": "Junior → Senior Developer",
      "progress_percentage": 65.0,
      "skills_completed": 13,
      "skills_total": 20,
      "status": "active"
    }
  ]
}
```

### 5.12.2 Crear Career Path Personalizado
```http
POST /career-paths 🔒
Authorization: Bearer {user_token}
Content-Type: application/json

{
  "name": "Mi Career Path Personalizado",
  "description": "Ruta personalizada para mis objetivos",
  "from_position": "Junior Developer",
  "to_position": "Tech Lead",
  "is_public": true,
  "skills": [
    {
      "skill_id": "skill_123",
      "position_x": 100,
      "position_y": 200,
      "is_starting_skill": true
    }
  ],
  "connections": [
    {
      "from_skill_id": "skill_123",
      "to_skill_id": "skill_456",
      "connection_type": "prerequisite"
    }
  ]
}
```

### 5.12.3 Activar Career Path
```http
POST /career-paths/{path_id}/activate 🔒
Authorization: Bearer {user_token}
```

### 5.12.4 Obtener Skills Repository
```http
GET /skills 🔒
Authorization: Bearer {user_token}
?area={area}&search={search_term}
```

**Response:**
```json
{
  "skills": [
    {
      "skill_id": "skill_123",
      "name": "JavaScript",
      "description": "Programming language for web development",
      "area": "programming",
      "color_class": "bg-yellow-500",
      "difficulty_level": "intermediate",
      "prerequisites": ["skill_456"]
    }
  ]
}
```

### 5.12.5 Actualizar Progreso de Skill
```http
POST /skills/{skill_id}/progress 🔒
Authorization: Bearer {user_token}
Content-Type: application/json

{
  "status": "completed",
  "verification_method": "course_completion",
  "notes": "Completado a través del curso de JavaScript Avanzado"
}
```

---

## 5.13 Sistema de Gamificación

### 5.13.1 Obtener Leaderboard
```http
GET /leaderboard 🔒
Authorization: Bearer {user_token}
?score_type={student|creator}&period={weekly|monthly|alltime}&limit={number}
```

**Response:**
```json
{
  "leaderboard": [
    {
      "position": 1,
      "user_id": "user_123",
      "username": "Carlos García",
      "avatar_url": "https://...",
      "total_points": 5240,
      "rank_change": 2,
      "previous_rank": 3,
      "percentile": 99.5,
      "metrics": {
        "courses_completed": 31,
        "forum_posts": 203,
        "courses_created": 5,
        "average_rating": 4.8
      }
    }
  ],
  "user_position": {
    "position": 47,
    "total_points": 2450,
    "rank_change": -3,
    "percentile": 65.2
  },
  "period_info": {
    "period_type": "weekly",
    "period_start": "2024-01-15",
    "period_end": "2024-01-21",
    "total_participants": 1247
  }
}
```

### 5.13.2 Obtener Puntos del Usuario
```http
GET /users/{user_id}/scores 🔒
Authorization: Bearer {user_token}
?period={weekly|monthly|alltime}
```

**Response:**
```json
{
  "student_score": {
    "total_points": 2450,
    "position": 47,
    "breakdown": {
      "course_completions": 1800,
      "forum_participation": 420,
      "course_ratings": 180,
      "career_path_bonus": 350
    }
  },
  "creator_score": {
    "total_points": 890,
    "position": 23,
    "breakdown": {
      "course_creation": 600,
      "student_completions": 200,
      "expert_validations": 90
    }
  },
  "recent_transactions": [
    {
      "transaction_id": "trans_123",
      "action_type": "course_completion",
      "points_awarded": 150,
      "bonus_multiplier": 1.5,
      "final_points": 225,
      "reference_type": "course",
      "reference_id": "course_456",
      "created_at": "2024-01-20T10:30:00Z"
    }
  ]
}
```

### 5.13.3 Historial de Transacciones de Puntos
```http
GET /users/{user_id}/score-transactions 🔒
Authorization: Bearer {user_token}
?limit={number}&offset={number}&action_type={type}
```

### 5.13.4 Validación de Experto
```http
POST /expert-validations 🔒
Authorization: Bearer {expert_token}
Content-Type: application/json

{
  "target_type": "course",
  "target_id": "course_123",
  "validation_type": "quality_review",
  "difficulty_level": "intermediate",
  "quality_rating": 5,
  "utility_rating": 4,
  "comments": "Excelente curso con contenido actualizado y ejercicios prácticos"
}
```

**Response:**
```json
{
  "validation_id": "validation_123",
  "status": "approved",
  "points_awarded_to_creator": 125,
  "bonus_multiplier_applied": 1.25,
  "created_at": "2024-01-20T15:45:00Z"
}
```

### 5.13.5 Estadísticas de Gamificación
```http
GET /gamification/stats 🔒
Authorization: Bearer {admin_token}
```

**Response:**
```json
{
  "total_points_awarded": 2450000,
  "active_competitors": {
    "weekly": 1247,
    "monthly": 3891,
    "alltime": 15623
  },
  "point_distribution": {
    "student_points": 1680000,
    "creator_points": 770000
  },
  "top_activities": [
    {
      "activity": "course_completion",
      "percentage": 45.2
    },
    {
      "activity": "forum_participation",
      "percentage": 28.7
    }
  ]
}
```

---

## 6. Banco de Preguntas

### 6.1 Crear Pregunta Manual
```http
POST /questions 🔒
Authorization: Bearer {content_creator_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "item_type": "writing",
  "prompt": "Describe a project management methodology you have used and explain its advantages and disadvantages.",
  "difficulty": "intermediate",
  "metadata": {
    "estimated_time_minutes": 15,
    "word_count_target": 150,
    "rubric": {
      "content": {"weight": 0.4, "max_score": 5},
      "organization": {"weight": 0.3, "max_score": 5},
      "clarity": {"weight": 0.3, "max_score": 5}
    }
  },
  "tags": ["project_management", "methodology", "analysis"]
}
```

### 6.2 Generar Preguntas con IA
```http
POST /questions/generate 🔒
Authorization: Bearer {content_creator_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "type": "listening",
  "prompt": "Create questions about hobbies and leisure activities",
  "count": 5,
  "difficulty": "B1",
  "style": "conversational",
  "metadata": {
    "speakers": 2,
    "duration_target": 60,
    "accent": "neutral"
  }
}
```

**Response (202):**
```json
{
  "job_id": "123e4567-e89b-12d3-a456-426614174004",
  "status": "pending",
  "estimated_completion": "2024-01-15T10:35:00Z",
  "webhook_url": "/webhooks/generation-complete"
}
```

### 6.3 Estado de Generación
```http
GET /ai/jobs/{job_id} 🔒
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "job_id": "123e4567-e89b-12d3-a456-426614174004",
  "status": "completed",
  "progress": 100,
  "result": {
    "questions_created": 5,
    "question_ids": ["q1", "q2", "q3", "q4", "q5"],
    "tokens_used": 1250,
    "cost_usd": 0.025
  },
  "completed_at": "2024-01-15T10:34:30Z"
}
```

---

## 7. Exámenes y Evaluación

### 7.1 Crear Examen
```http
POST /exams 🔒
Authorization: Bearer {content_creator_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "course_id": "123e4567-e89b-12d3-a456-426614174003",
  "title": "English Placement Test",
  "description": "Comprehensive assessment of English proficiency",
  "time_limit_sec": 3600,
  "settings": {
    "randomize_questions": true,
    "allow_review": false,
    "attempts_allowed": 1,
    "show_results_immediately": true
  }
}
```

### 7.2 Asignar Preguntas
```http
PUT /exams/{exam_id}/items 🔒
Authorization: Bearer {content_creator_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "question_ids": [
    "123e4567-e89b-12d3-a456-426614174005",
    "123e4567-e89b-12d3-a456-426614174006"
  ],
  "order": "random"
}
```

### 7.3 Iniciar Intento de Examen
```http
POST /exam-attempts 🌐
Content-Type: application/json
```

**Request Body:**
```json
{
  "exam_id": "123e4567-e89b-12d3-a456-426614174007",
  "access_token": "temp_token_for_guest_user"
}
```

**Response (201):**
```json
{
  "attempt_id": "123e4567-e89b-12d3-a456-426614174008",
  "exam_title": "English Placement Test",
  "time_limit_sec": 3600,
  "total_questions": 25,
  "started_at": "2024-01-15T10:30:00Z",
  "expires_at": "2024-01-15T11:30:00Z"
}
```

### 7.4 Obtener Siguiente Pregunta
```http
GET /attempts/{attempt_id}/next 🔒
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "question_number": 1,
  "total_questions": 25,
  "question": {
    "question_id": "123e4567-e89b-12d3-a456-426614174005",
    "type": "writing",
    "prompt": "Describe your ideal vacation destination...",
    "time_limit_sec": 900,
    "metadata": {
      "word_count_target": 150
    }
  },
  "time_remaining_sec": 3540
}
```

### 7.5 Enviar Respuesta
```http
POST /answers 🔒
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "attempt_id": "123e4567-e89b-12d3-a456-426614174008",
  "question_id": "123e4567-e89b-12d3-a456-426614174005",
  "answer_data": {
    "text": "My ideal vacation destination would be Japan because...",
    "time_spent_sec": 720
  }
}
```

### 7.6 Finalizar Examen
```http
POST /attempts/{attempt_id}/finish 🔒
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "attempt_id": "123e4567-e89b-12d3-a456-426614174008",
  "status": "completed",
  "submitted_at": "2024-01-15T11:15:00Z",
  "total_time_sec": 2700,
  "questions_answered": 25,
  "preliminary_score": 78.5,
  "estimated_cefr": "B2",
  "detailed_results_available_at": "2024-01-15T11:20:00Z"
}
```

---

## 8. Resultados y Analítica

### 8.1 Obtener Resultados de Examen
```http
GET /reports/exams/{exam_id} 🔒
Authorization: Bearer {token}
```

**Query Parameters:**
- `user_id`: Filtrar por usuario específico
- `from_date`: Fecha inicio (ISO 8601)
- `to_date`: Fecha fin (ISO 8601)
- `format`: json, csv, pdf

**Response (200):**
```json
{
  "exam_id": "123e4567-e89b-12d3-a456-426614174007",
  "exam_title": "English Placement Test",
  "attempts": [
    {
      "attempt_id": "123e4567-e89b-12d3-a456-426614174008",
      "user_email": "<EMAIL>",
      "started_at": "2024-01-15T10:30:00Z",
      "completed_at": "2024-01-15T11:15:00Z",
      "total_score": 82.3,
      "cefr_level": "B2",
      "breakdown": {
        "writing": 85.0,
        "listening": 78.0,
        "speaking": 84.0
      }
    }
  ],
  "statistics": {
    "total_attempts": 156,
    "average_score": 76.8,
    "completion_rate": 94.2,
    "average_time_minutes": 48
  }
}
```

### 8.2 Dashboard de Métricas
```http
GET /metrics 🔒
Authorization: Bearer {token}
```

**Query Parameters:**
- `period`: day, week, month, year
- `metric_types`: users, exams, ai_usage, costs

---

## 9. Webhooks y Integraciones

### 9.1 Configurar Webhook
```http
POST /webhooks 🔒
Authorization: Bearer {admin_tenant_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "url": "https://your-system.com/webhooks/arroyo",
  "events": ["exam.completed", "user.created", "payment.succeeded"],
  "secret": "webhook_secret_key_here",
  "active": true
}
```

### 9.2 Ejemplo de Payload de Webhook
```json
{
  "event": "exam.completed",
  "timestamp": "2024-01-15T11:20:00Z",
  "data": {
    "attempt_id": "123e4567-e89b-12d3-a456-426614174008",
    "user_email": "<EMAIL>",
    "exam_title": "English Placement Test",
    "score": 82.3,
    "cefr_level": "B2"
  },
  "tenant_id": "123e4567-e89b-12d3-a456-426614174001"
}
```

---

## 10. Rate Limiting y Cuotas

### 10.1 Headers de Rate Limiting
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642248000
```

### 10.2 Límites por Plan
| Plan | Requests/min | AI Generations/month | Storage GB |
|------|--------------|---------------------|------------|
| Free | 100 | 50 | 1 |
| Basic | 500 | 500 | 10 |
| Premium | 2000 | 2000 | 100 |
| Enterprise | 10000 | Unlimited | 1000 |

---

## Conclusión

Esta API Reference proporciona la documentación completa para integrar con Arroyo University. Para ejemplos adicionales, SDKs y documentación interactiva, visite nuestra [documentación completa](https://docs.arroyo.app) o explore nuestro [Postman Collection](https://postman.com/arroyo-university).
