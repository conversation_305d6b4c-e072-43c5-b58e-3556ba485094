"""
Analytics Router
Handles analytics and reporting API endpoints including user metrics, course analytics, and leaderboards
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlmodel import Session
from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime, date

from ..core.database import get_session
from ..core.security import get_current_user, require_permissions
from ..models.user_models import User
from ..models.analytics_models import (
    UserScore, UserScoreCreate, UserScoreUpdate,
    UserMetrics, UserMetricsCreate, UserMetricsUpdate,
    LeaderboardEntry, LeaderboardEntryCreate,
    SystemMetrics, SystemMetricsCreate
)
from ..services.analytics_service import AnalyticsService

router = APIRouter()

# User Analytics
@router.get("/users/{user_id}/metrics", response_model=UserMetrics)
async def get_user_metrics(
    user_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get user metrics"""
    # Users can only view their own metrics unless they have analytics permissions
    if user_id != current_user.id and not current_user.has_permission("analytics_view"):
        raise HTTPException(status_code=403, detail="Not authorized to view user metrics")
    
    analytics_service = AnalyticsService(session)
    metrics = await analytics_service.get_user_metrics(user_id)
    if not metrics:
        raise HTTPException(status_code=404, detail="User metrics not found")
    return metrics

@router.get("/users/{user_id}/scores", response_model=List[UserScore])
async def get_user_scores(
    user_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    score_type: Optional[str] = None,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get user scores"""
    # Users can only view their own scores unless they have analytics permissions
    if user_id != current_user.id and not current_user.has_permission("analytics_view"):
        raise HTTPException(status_code=403, detail="Not authorized to view user scores")
    
    analytics_service = AnalyticsService(session)
    return await analytics_service.get_user_scores(
        user_id=user_id,
        skip=skip,
        limit=limit,
        score_type=score_type
    )

@router.post("/users/{user_id}/scores", response_model=UserScore, status_code=status.HTTP_201_CREATED)
async def create_user_score(
    user_id: UUID,
    score_data: UserScoreCreate,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["analytics_manage"]))
):
    """Create a user score entry"""
    analytics_service = AnalyticsService(session)
    score_data.user_id = user_id
    return await analytics_service.create_user_score(score_data)

@router.get("/users/{user_id}/progress", response_model=Dict[str, Any])
async def get_user_progress(
    user_id: UUID,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get user learning progress over time"""
    # Users can only view their own progress unless they have analytics permissions
    if user_id != current_user.id and not current_user.has_permission("analytics_view"):
        raise HTTPException(status_code=403, detail="Not authorized to view user progress")
    
    analytics_service = AnalyticsService(session)
    return await analytics_service.get_user_progress(
        user_id=user_id,
        start_date=start_date,
        end_date=end_date
    )

# Course Analytics
@router.get("/courses/{course_id}/analytics", response_model=Dict[str, Any])
async def get_course_analytics(
    course_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["analytics_view"]))
):
    """Get course analytics"""
    analytics_service = AnalyticsService(session)
    return await analytics_service.get_course_analytics(course_id)

@router.get("/courses/{course_id}/enrollment-stats", response_model=Dict[str, Any])
async def get_course_enrollment_stats(
    course_id: UUID,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["analytics_view"]))
):
    """Get course enrollment statistics"""
    analytics_service = AnalyticsService(session)
    return await analytics_service.get_course_enrollment_stats(
        course_id=course_id,
        start_date=start_date,
        end_date=end_date
    )

@router.get("/courses/{course_id}/completion-stats", response_model=Dict[str, Any])
async def get_course_completion_stats(
    course_id: UUID,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["analytics_view"]))
):
    """Get course completion statistics"""
    analytics_service = AnalyticsService(session)
    return await analytics_service.get_course_completion_stats(
        course_id=course_id,
        start_date=start_date,
        end_date=end_date
    )

# Leaderboards
@router.get("/leaderboards/students", response_model=List[LeaderboardEntry])
async def get_student_leaderboard(
    period: str = Query("total", regex="^(weekly|monthly|total)$"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get student leaderboard"""
    analytics_service = AnalyticsService(session)
    return await analytics_service.get_student_leaderboard(
        tenant_id=current_user.tenant_id,
        period=period,
        skip=skip,
        limit=limit
    )

@router.get("/leaderboards/creators", response_model=List[LeaderboardEntry])
async def get_creator_leaderboard(
    period: str = Query("total", regex="^(weekly|monthly|total)$"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get course creator leaderboard"""
    analytics_service = AnalyticsService(session)
    return await analytics_service.get_creator_leaderboard(
        tenant_id=current_user.tenant_id,
        period=period,
        skip=skip,
        limit=limit
    )

@router.get("/leaderboards/my-position", response_model=Dict[str, Any])
async def get_my_leaderboard_position(
    leaderboard_type: str = Query("student", regex="^(student|creator)$"),
    period: str = Query("total", regex="^(weekly|monthly|total)$"),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get current user's position in leaderboards"""
    analytics_service = AnalyticsService(session)
    return await analytics_service.get_user_leaderboard_position(
        user_id=current_user.id,
        leaderboard_type=leaderboard_type,
        period=period
    )

# System Analytics
@router.get("/system/metrics", response_model=SystemMetrics)
async def get_system_metrics(
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["system_analytics"]))
):
    """Get system-wide metrics"""
    analytics_service = AnalyticsService(session)
    return await analytics_service.get_system_metrics(current_user.tenant_id)

@router.get("/system/dashboard", response_model=Dict[str, Any])
async def get_system_dashboard(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["system_analytics"]))
):
    """Get system dashboard data"""
    analytics_service = AnalyticsService(session)
    return await analytics_service.get_system_dashboard(
        tenant_id=current_user.tenant_id,
        start_date=start_date,
        end_date=end_date
    )

@router.get("/system/usage-stats", response_model=Dict[str, Any])
async def get_usage_statistics(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    granularity: str = Query("daily", regex="^(hourly|daily|weekly|monthly)$"),
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["system_analytics"]))
):
    """Get platform usage statistics"""
    analytics_service = AnalyticsService(session)
    return await analytics_service.get_usage_statistics(
        tenant_id=current_user.tenant_id,
        start_date=start_date,
        end_date=end_date,
        granularity=granularity
    )

# Reports
@router.get("/reports/user-engagement", response_model=Dict[str, Any])
async def get_user_engagement_report(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["analytics_view"]))
):
    """Get user engagement report"""
    analytics_service = AnalyticsService(session)
    return await analytics_service.get_user_engagement_report(
        tenant_id=current_user.tenant_id,
        start_date=start_date,
        end_date=end_date
    )

@router.get("/reports/course-performance", response_model=Dict[str, Any])
async def get_course_performance_report(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["analytics_view"]))
):
    """Get course performance report"""
    analytics_service = AnalyticsService(session)
    return await analytics_service.get_course_performance_report(
        tenant_id=current_user.tenant_id,
        start_date=start_date,
        end_date=end_date
    )

@router.post("/reports/export", response_model=Dict[str, Any])
async def export_analytics_report(
    report_type: str,
    format: str = Query("json", regex="^(json|csv|xlsx)$"),
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["analytics_export"]))
):
    """Export analytics report"""
    analytics_service = AnalyticsService(session)
    return await analytics_service.export_report(
        report_type=report_type,
        format=format,
        tenant_id=current_user.tenant_id,
        start_date=start_date,
        end_date=end_date
    )
