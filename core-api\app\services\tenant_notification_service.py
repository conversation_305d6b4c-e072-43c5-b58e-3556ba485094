"""
Tenant notification configuration service
"""

import json
import base64
from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID

from sqlmodel import Session, select
from cryptography.fernet import Fernet

from ..models.tenant_notification_config import (
    TenantNotificationConfig, TenantNotificationConfigCreate, TenantNotificationConfigUpdate,
    TenantNotificationRule, TenantNotificationRuleCreate, TenantNotificationRuleUpdate,
    NotificationChannelType, NotificationConfigStatus, NOTIFICATION_CHANNEL_TEMPLATES
)


class TenantNotificationConfigService:
    """Service for managing tenant notification configurations"""
    
    def __init__(self, session: Session, encryption_key: Optional[str] = None):
        self.session = session
        self.encryption_key = encryption_key
        if encryption_key:
            self.cipher = Fernet(encryption_key.encode() if isinstance(encryption_key, str) else encryption_key)
        else:
            self.cipher = None
    
    def _encrypt_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Encrypt sensitive configuration data"""
        if not self.cipher:
            return data
        
        sensitive_fields = {
            "smtp_password", "auth_token", "bot_token", "webhook_url", 
            "secret", "api_key", "private_key", "fcm_server_key"
        }
        
        encrypted_data = data.copy()
        for key, value in data.items():
            if key in sensitive_fields and value:
                encrypted_value = self.cipher.encrypt(str(value).encode())
                encrypted_data[key] = base64.b64encode(encrypted_value).decode()
                encrypted_data[f"{key}_encrypted"] = True
        
        return encrypted_data
    
    def _decrypt_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Decrypt sensitive configuration data"""
        if not self.cipher:
            return data
        
        decrypted_data = data.copy()
        for key, value in data.items():
            if key.endswith("_encrypted") and value:
                original_key = key.replace("_encrypted", "")
                if original_key in data:
                    try:
                        encrypted_value = base64.b64decode(data[original_key].encode())
                        decrypted_value = self.cipher.decrypt(encrypted_value).decode()
                        decrypted_data[original_key] = decrypted_value
                        del decrypted_data[key]  # Remove the encryption flag
                    except Exception:
                        # If decryption fails, keep original value
                        pass
        
        return decrypted_data
    
    def _mask_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Mask sensitive data for API responses"""
        sensitive_fields = {
            "smtp_password", "auth_token", "bot_token", "secret", 
            "api_key", "private_key", "fcm_server_key"
        }
        
        masked_data = data.copy()
        for key, value in data.items():
            if key in sensitive_fields and value:
                if len(str(value)) > 8:
                    masked_data[key] = f"{str(value)[:4]}...{str(value)[-4:]}"
                else:
                    masked_data[key] = "***"
        
        return masked_data
    
    async def get_channel_templates(self) -> Dict[str, Any]:
        """Get available notification channel templates"""
        return {
            channel_type.value: {
                "name": template.name,
                "description": template.description,
                "required_fields": template.required_fields,
                "optional_fields": template.optional_fields,
                "field_descriptions": template.field_descriptions,
                "example_config": template.example_config
            }
            for channel_type, template in NOTIFICATION_CHANNEL_TEMPLATES.items()
        }
    
    async def create_config(
        self, 
        tenant_id: UUID, 
        config_data: TenantNotificationConfigCreate
    ) -> TenantNotificationConfig:
        """Create a new notification configuration"""
        
        # Encrypt sensitive data
        encrypted_config_data = self._encrypt_sensitive_data(config_data.config_data)
        
        config = TenantNotificationConfig(
            tenant_id=tenant_id,
            channel_type=config_data.channel_type,
            is_enabled=config_data.is_enabled,
            status=config_data.status,
            name=config_data.name,
            description=config_data.description,
            priority=config_data.priority,
            config_data=encrypted_config_data
        )
        
        self.session.add(config)
        self.session.commit()
        self.session.refresh(config)
        
        return config
    
    async def get_configs(
        self, 
        tenant_id: UUID, 
        channel_type: Optional[NotificationChannelType] = None,
        is_enabled: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[TenantNotificationConfig]:
        """Get notification configurations for a tenant"""
        
        query = select(TenantNotificationConfig).where(
            TenantNotificationConfig.tenant_id == tenant_id
        )
        
        if channel_type:
            query = query.where(TenantNotificationConfig.channel_type == channel_type)
        
        if is_enabled is not None:
            query = query.where(TenantNotificationConfig.is_enabled == is_enabled)
        
        query = query.offset(skip).limit(limit)
        result = self.session.exec(query)
        configs = result.all()
        
        # Mask sensitive data for response
        for config in configs:
            config.config_data = self._mask_sensitive_data(config.config_data)
        
        return configs
    
    async def get_config(self, tenant_id: UUID, config_id: UUID) -> Optional[TenantNotificationConfig]:
        """Get a specific notification configuration"""
        
        query = select(TenantNotificationConfig).where(
            TenantNotificationConfig.tenant_id == tenant_id,
            TenantNotificationConfig.config_id == config_id
        )
        
        result = self.session.exec(query)
        config = result.first()
        
        if config:
            # Mask sensitive data for response
            config.config_data = self._mask_sensitive_data(config.config_data)
        
        return config
    
    async def get_config_for_use(self, tenant_id: UUID, config_id: UUID) -> Optional[TenantNotificationConfig]:
        """Get a configuration with decrypted data for actual use"""
        
        query = select(TenantNotificationConfig).where(
            TenantNotificationConfig.tenant_id == tenant_id,
            TenantNotificationConfig.config_id == config_id,
            TenantNotificationConfig.is_enabled == True,
            TenantNotificationConfig.status == NotificationConfigStatus.ACTIVE
        )
        
        result = self.session.exec(query)
        config = result.first()
        
        if config:
            # Decrypt sensitive data for use
            config.config_data = self._decrypt_sensitive_data(config.config_data)
        
        return config
    
    async def update_config(
        self, 
        tenant_id: UUID, 
        config_id: UUID, 
        config_data: TenantNotificationConfigUpdate
    ) -> Optional[TenantNotificationConfig]:
        """Update a notification configuration"""
        
        query = select(TenantNotificationConfig).where(
            TenantNotificationConfig.tenant_id == tenant_id,
            TenantNotificationConfig.config_id == config_id
        )
        
        result = self.session.exec(query)
        config = result.first()
        
        if not config:
            return None
        
        # Update fields
        update_data = config_data.model_dump(exclude_unset=True)
        
        # Encrypt sensitive data if config_data is being updated
        if "config_data" in update_data and update_data["config_data"]:
            update_data["config_data"] = self._encrypt_sensitive_data(update_data["config_data"])
        
        for field, value in update_data.items():
            setattr(config, field, value)
        
        config.updated_at = datetime.utcnow()
        
        self.session.add(config)
        self.session.commit()
        self.session.refresh(config)
        
        # Mask sensitive data for response
        config.config_data = self._mask_sensitive_data(config.config_data)
        
        return config
    
    async def delete_config(self, tenant_id: UUID, config_id: UUID) -> bool:
        """Delete a notification configuration"""
        
        query = select(TenantNotificationConfig).where(
            TenantNotificationConfig.tenant_id == tenant_id,
            TenantNotificationConfig.config_id == config_id
        )
        
        result = self.session.exec(query)
        config = result.first()
        
        if not config:
            return False
        
        self.session.delete(config)
        self.session.commit()
        
        return True
    
    async def test_config(self, tenant_id: UUID, config_id: UUID) -> Dict[str, Any]:
        """Test a notification configuration"""
        
        config = await self.get_config_for_use(tenant_id, config_id)
        if not config:
            return {"success": False, "error": "Configuration not found"}
        
        try:
            # Test based on channel type
            if config.channel_type == NotificationChannelType.EMAIL:
                result = await self._test_email_config(config.config_data)
            elif config.channel_type == NotificationChannelType.SMS:
                result = await self._test_sms_config(config.config_data)
            elif config.channel_type == NotificationChannelType.SLACK:
                result = await self._test_slack_config(config.config_data)
            elif config.channel_type == NotificationChannelType.TEAMS:
                result = await self._test_teams_config(config.config_data)
            elif config.channel_type == NotificationChannelType.WEBHOOK:
                result = await self._test_webhook_config(config.config_data)
            else:
                result = {"success": False, "error": "Channel type not supported for testing"}
            
            # Update test results
            config.last_test_at = datetime.utcnow()
            config.last_test_result = "success" if result["success"] else "failed"
            if not result["success"]:
                config.last_error = result.get("error", "Unknown error")
                config.status = NotificationConfigStatus.ERROR
            else:
                config.last_error = None
                if config.status == NotificationConfigStatus.ERROR:
                    config.status = NotificationConfigStatus.ACTIVE
            
            self.session.add(config)
            self.session.commit()
            
            return result
            
        except Exception as e:
            # Update error status
            config.last_test_at = datetime.utcnow()
            config.last_test_result = "failed"
            config.last_error = str(e)
            config.status = NotificationConfigStatus.ERROR
            
            self.session.add(config)
            self.session.commit()
            
            return {"success": False, "error": str(e)}
    
    async def _test_email_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Test email configuration"""
        # Implementation would test SMTP connection
        return {"success": True, "message": "Email configuration test successful"}
    
    async def _test_sms_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Test SMS configuration"""
        # Implementation would test Twilio connection
        return {"success": True, "message": "SMS configuration test successful"}
    
    async def _test_slack_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Test Slack configuration"""
        # Implementation would test Slack API connection
        return {"success": True, "message": "Slack configuration test successful"}
    
    async def _test_teams_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Test Teams configuration"""
        # Implementation would test Teams webhook
        return {"success": True, "message": "Teams configuration test successful"}
    
    async def _test_webhook_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Test webhook configuration"""
        # Implementation would test webhook endpoint
        return {"success": True, "message": "Webhook configuration test successful"}
