# Prometheus Configuration for Arroyo University

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'arroyo-dev'
    environment: 'development'

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Core API Service
  - job_name: 'core-api'
    static_configs:
      - targets: ['core-api:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Notification Service
  - job_name: 'notification-service'
    static_configs:
      - targets: ['notification-service:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # PostgreSQL Database
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Node Exporter (if running)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Nginx/API Gateway
  - job_name: 'nginx'
    static_configs:
      - targets: ['api-gateway:9113']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # MinIO Object Storage
  - job_name: 'minio'
    static_configs:
      - targets: ['minio:9000']
    metrics_path: '/minio/v2/metrics/cluster'
    scrape_interval: 30s

# Remote write configuration (for production)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint"
#     basic_auth:
#       username: "username"
#       password: "password"

# Storage configuration
storage:
  tsdb:
    retention.time: 15d
    retention.size: 10GB
