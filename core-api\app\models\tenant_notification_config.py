"""
Tenant-specific notification configuration models
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from uuid import UUID
from enum import Enum

from sqlmodel import SQLModel, Field

from .base import TimestampMixin, TenantMixin


class NotificationChannelType(str, Enum):
    """Notification channel types"""
    EMAIL = "email"
    SMS = "sms"
    SLACK = "slack"
    TEAMS = "teams"
    WEBHOOK = "webhook"
    PUSH = "push"
    IN_APP = "in_app"


class NotificationConfigStatus(str, Enum):
    """Notification configuration status"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    TESTING = "testing"
    ERROR = "error"


class TenantNotificationConfigBase(SQLModel):
    """Base tenant notification configuration model"""
    channel_type: NotificationChannelType
    is_enabled: bool = Field(default=False)
    status: NotificationConfigStatus = Field(default=NotificationConfigStatus.INACTIVE)
    name: str = Field(max_length=255)
    description: Optional[str] = Field(default=None, max_length=500)
    priority: int = Field(default=5, ge=1, le=10)  # 1 = highest, 10 = lowest


class TenantNotificationConfig(TenantNotificationConfigBase, TenantMixin, TimestampMixin, table=True):
    """Tenant notification configuration table model"""
    __tablename__ = "tenant_notification_configs"
    
    config_id: UUID = Field(primary_key=True)
    
    # Configuration data (encrypted sensitive data)
    config_data: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSONB"})
    
    # Rate limiting settings
    rate_limit_per_hour: Optional[int] = Field(default=None)
    rate_limit_per_day: Optional[int] = Field(default=None)
    
    # Retry settings
    max_retry_attempts: int = Field(default=3)
    retry_delay_seconds: int = Field(default=60)
    
    # Testing and validation
    last_test_at: Optional[datetime] = None
    last_test_result: Optional[str] = None
    last_error: Optional[str] = None
    
    # Usage statistics
    total_sent: int = Field(default=0)
    total_failed: int = Field(default=0)
    last_used_at: Optional[datetime] = None


class TenantNotificationConfigCreate(TenantNotificationConfigBase):
    """Tenant notification configuration creation model"""
    config_data: Dict[str, Any]


class TenantNotificationConfigUpdate(SQLModel):
    """Tenant notification configuration update model"""
    is_enabled: Optional[bool] = None
    status: Optional[NotificationConfigStatus] = None
    name: Optional[str] = None
    description: Optional[str] = None
    priority: Optional[int] = None
    config_data: Optional[Dict[str, Any]] = None
    rate_limit_per_hour: Optional[int] = None
    rate_limit_per_day: Optional[int] = None
    max_retry_attempts: Optional[int] = None
    retry_delay_seconds: Optional[int] = None


class TenantNotificationConfigResponse(TenantNotificationConfigBase):
    """Tenant notification configuration response model"""
    config_id: UUID
    tenant_id: UUID
    config_data: Dict[str, Any]  # Sensitive data will be masked in service layer
    rate_limit_per_hour: Optional[int]
    rate_limit_per_day: Optional[int]
    max_retry_attempts: int
    retry_delay_seconds: int
    last_test_at: Optional[datetime]
    last_test_result: Optional[str]
    last_error: Optional[str]
    total_sent: int
    total_failed: int
    last_used_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime


class NotificationChannelTemplate(SQLModel):
    """Template for notification channel configuration"""
    channel_type: NotificationChannelType
    name: str
    description: str
    required_fields: List[str]
    optional_fields: List[str]
    field_descriptions: Dict[str, str]
    example_config: Dict[str, Any]
    test_endpoint: Optional[str] = None


# Predefined channel templates
NOTIFICATION_CHANNEL_TEMPLATES = {
    NotificationChannelType.EMAIL: NotificationChannelTemplate(
        channel_type=NotificationChannelType.EMAIL,
        name="Email (SMTP)",
        description="Send notifications via email using SMTP server",
        required_fields=["smtp_host", "smtp_port", "smtp_user", "smtp_password", "from_email"],
        optional_fields=["smtp_tls", "smtp_ssl", "from_name", "reply_to"],
        field_descriptions={
            "smtp_host": "SMTP server hostname (e.g., smtp.gmail.com)",
            "smtp_port": "SMTP server port (587 for TLS, 465 for SSL, 25 for plain)",
            "smtp_user": "SMTP username/email",
            "smtp_password": "SMTP password or app password",
            "from_email": "From email address",
            "smtp_tls": "Enable TLS encryption (recommended)",
            "smtp_ssl": "Enable SSL encryption",
            "from_name": "Display name for sender",
            "reply_to": "Reply-to email address"
        },
        example_config={
            "smtp_host": "smtp.gmail.com",
            "smtp_port": 587,
            "smtp_user": "<EMAIL>",
            "smtp_password": "your-app-password",
            "from_email": "<EMAIL>",
            "from_name": "Your Organization",
            "smtp_tls": True,
            "smtp_ssl": False
        }
    ),
    
    NotificationChannelType.SMS: NotificationChannelTemplate(
        channel_type=NotificationChannelType.SMS,
        name="SMS (Twilio)",
        description="Send SMS notifications using Twilio service",
        required_fields=["account_sid", "auth_token", "phone_number"],
        optional_fields=["messaging_service_sid"],
        field_descriptions={
            "account_sid": "Twilio Account SID",
            "auth_token": "Twilio Auth Token",
            "phone_number": "Twilio phone number (e.g., +**********)",
            "messaging_service_sid": "Twilio Messaging Service SID (optional)"
        },
        example_config={
            "account_sid": "ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
            "auth_token": "your-auth-token",
            "phone_number": "+**********"
        }
    ),
    
    NotificationChannelType.SLACK: NotificationChannelTemplate(
        channel_type=NotificationChannelType.SLACK,
        name="Slack",
        description="Send notifications to Slack channels",
        required_fields=["bot_token"],
        optional_fields=["default_channel", "signing_secret"],
        field_descriptions={
            "bot_token": "Slack Bot User OAuth Token (starts with xoxb-)",
            "default_channel": "Default channel for notifications (e.g., #general)",
            "signing_secret": "Slack Signing Secret for webhook verification"
        },
        example_config={
            "bot_token": "xoxb-your-bot-token",
            "default_channel": "#notifications",
            "signing_secret": "your-signing-secret"
        }
    ),
    
    NotificationChannelType.TEAMS: NotificationChannelTemplate(
        channel_type=NotificationChannelType.TEAMS,
        name="Microsoft Teams",
        description="Send notifications to Microsoft Teams channels",
        required_fields=["webhook_url"],
        optional_fields=["theme_color"],
        field_descriptions={
            "webhook_url": "Microsoft Teams Incoming Webhook URL",
            "theme_color": "Default theme color for messages (hex color)"
        },
        example_config={
            "webhook_url": "https://outlook.office.com/webhook/...",
            "theme_color": "#0078D4"
        }
    ),
    
    NotificationChannelType.WEBHOOK: NotificationChannelTemplate(
        channel_type=NotificationChannelType.WEBHOOK,
        name="Custom Webhook",
        description="Send notifications to custom webhook endpoints",
        required_fields=["url"],
        optional_fields=["secret", "headers", "method"],
        field_descriptions={
            "url": "Webhook endpoint URL",
            "secret": "Secret for webhook signature verification",
            "headers": "Custom headers to include in requests",
            "method": "HTTP method (POST, PUT, PATCH)"
        },
        example_config={
            "url": "https://your-webhook-endpoint.com/notifications",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json",
                "Authorization": "Bearer your-token"
            },
            "secret": "your-webhook-secret"
        }
    ),
    
    NotificationChannelType.PUSH: NotificationChannelTemplate(
        channel_type=NotificationChannelType.PUSH,
        name="Push Notifications",
        description="Send push notifications to mobile devices",
        required_fields=["fcm_server_key"],
        optional_fields=["fcm_sender_id", "apns_key_id", "apns_team_id", "apns_bundle_id"],
        field_descriptions={
            "fcm_server_key": "Firebase Cloud Messaging Server Key",
            "fcm_sender_id": "Firebase Cloud Messaging Sender ID",
            "apns_key_id": "Apple Push Notification Service Key ID",
            "apns_team_id": "Apple Developer Team ID",
            "apns_bundle_id": "iOS App Bundle ID"
        },
        example_config={
            "fcm_server_key": "your-fcm-server-key",
            "fcm_sender_id": "your-sender-id",
            "apns_bundle_id": "com.yourcompany.yourapp"
        }
    )
}


class TenantNotificationRule(SQLModel, table=True):
    """Tenant notification rules for automatic notifications"""
    __tablename__ = "tenant_notification_rules"
    
    rule_id: UUID = Field(primary_key=True)
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)
    
    name: str = Field(max_length=255)
    description: Optional[str] = Field(default=None, max_length=500)
    is_enabled: bool = Field(default=True)
    
    # Trigger conditions
    event_type: str = Field(max_length=100)  # course_enrollment, exam_graded, etc.
    conditions: Dict[str, Any] = Field(default_factory=dict, sa_column_kwargs={"type_": "JSONB"})
    
    # Notification settings
    channels: List[NotificationChannelType] = Field(sa_column_kwargs={"type_": "JSONB"})
    template_name: Optional[str] = Field(default=None, max_length=255)
    delay_minutes: int = Field(default=0)
    
    # Frequency control
    max_per_user_per_day: Optional[int] = None
    cooldown_minutes: Optional[int] = None
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class TenantNotificationRuleCreate(SQLModel):
    """Tenant notification rule creation model"""
    name: str
    description: Optional[str] = None
    is_enabled: bool = True
    event_type: str
    conditions: Dict[str, Any] = Field(default_factory=dict)
    channels: List[NotificationChannelType]
    template_name: Optional[str] = None
    delay_minutes: int = 0
    max_per_user_per_day: Optional[int] = None
    cooldown_minutes: Optional[int] = None


class TenantNotificationRuleUpdate(SQLModel):
    """Tenant notification rule update model"""
    name: Optional[str] = None
    description: Optional[str] = None
    is_enabled: Optional[bool] = None
    event_type: Optional[str] = None
    conditions: Optional[Dict[str, Any]] = None
    channels: Optional[List[NotificationChannelType]] = None
    template_name: Optional[str] = None
    delay_minutes: Optional[int] = None
    max_per_user_per_day: Optional[int] = None
    cooldown_minutes: Optional[int] = None
