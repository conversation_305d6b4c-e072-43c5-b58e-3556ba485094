#!/bin/bash

# Arroyo University Development Setup Script
# This script sets up the development environment

set -e

echo "🚀 Setting up Arroyo University Development Environment"
echo "=================================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

echo "✅ Docker is running"

# Create .env files if they don't exist
echo "📝 Creating environment files..."

if [ ! -f "core-api/.env" ]; then
    cp core-api/.env.example core-api/.env
    echo "✅ Created core-api/.env"
else
    echo "ℹ️  core-api/.env already exists"
fi

if [ ! -f "frontend/.env" ]; then
    cat > frontend/.env << EOF
# Frontend Environment Variables
VITE_API_BASE_URL=http://localhost:80/api/v1
VITE_APP_NAME=Arroyo University
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=development
EOF
    echo "✅ Created frontend/.env"
else
    echo "ℹ️  frontend/.env already exists"
fi

if [ ! -f "notification-service/.env" ]; then
    cat > notification-service/.env << EOF
# Notification Service Environment Variables
APP_NAME=Arroyo University Notification Service
APP_VERSION=1.0.0
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Database
DATABASE_URL=***********************************************/arroyo_university

# Redis
REDIS_URL=redis://:redis123@redis:6379/2

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
SMTP_SSL=false

# Slack Configuration
SLACK_BOT_TOKEN=your-slack-bot-token
SLACK_SIGNING_SECRET=your-slack-signing-secret

# Twilio Configuration
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number
EOF
    echo "✅ Created notification-service/.env"
else
    echo "ℹ️  notification-service/.env already exists"
fi

if [ ! -f "api-gateway/.env" ]; then
    cat > api-gateway/.env << EOF
# API Gateway Environment Variables
ENVIRONMENT=development
CORE_API_URL=http://core-api:8000
NOTIFICATION_SERVICE_URL=http://notification-service:8000
FRONTEND_URL=http://frontend:3000
EOF
    echo "✅ Created api-gateway/.env"
else
    echo "ℹ️  api-gateway/.env already exists"
fi

# Build and start services
echo "🔨 Building Docker images..."
docker-compose build

echo "🚀 Starting services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check service health
echo "🔍 Checking service health..."

# Wait for database to be ready
echo "   Waiting for database..."
until docker-compose exec -T postgres pg_isready -U postgres > /dev/null 2>&1; do
    echo "   Database not ready, waiting..."
    sleep 2
done
echo "✅ Database is ready"

# Wait for core API to be ready
echo "   Waiting for Core API..."
until curl -f http://localhost:8000/health > /dev/null 2>&1; do
    echo "   Core API not ready, waiting..."
    sleep 2
done
echo "✅ Core API is ready"

# Run database migrations
echo "🗄️  Running database migrations..."
docker-compose exec -T core-api python -m alembic upgrade head

# Seed database with sample data (optional)
read -p "🌱 Do you want to seed the database with sample data? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🌱 Seeding database..."
    docker-compose exec -T core-api python scripts/seed_data.py || echo "⚠️  Seeding failed or script not found"
fi

echo ""
echo "🎉 Development environment setup complete!"
echo ""
echo "📋 Service URLs:"
echo "   Frontend:     http://localhost:3000"
echo "   API Gateway:  http://localhost:80"
echo "   Core API:     http://localhost:8000"
echo "   API Docs:     http://localhost:8000/docs"
echo "   Grafana:      http://localhost:3001 (admin/admin123)"
echo "   Prometheus:   http://localhost:9090"
echo "   MinIO:        http://localhost:9001 (minioadmin/minioadmin123)"
echo ""
echo "🔧 Useful commands:"
echo "   View logs:    make logs-follow"
echo "   Stop all:     make down"
echo "   Restart API:  make quick-restart-api"
echo "   Database:     make shell-db"
echo ""
echo "📖 Check the README.md for more information"
