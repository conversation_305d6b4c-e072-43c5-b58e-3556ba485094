import React, { useState, useRef, useEffect } from 'react';
import {
  Save, RefreshCw, Upload, Eye, Palette, Globe,
  Building, Image, Type, Monitor, Smartphone, Tablet,
  Copy, Check, ExternalLink, Download
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Switch } from '@/components/ui/Switch';
import { Badge } from '@/components/ui/Badge';
import { SimpleFileUpload } from '@/components/ui/FileUpload';
import toast from 'react-hot-toast';
import { brandingApi, type TenantBranding } from '@/services/brandingApi';

const defaultBranding: TenantBranding = {
  organization_name: 'Mi Organización',
  display_name: 'Mi Organización - Plataforma de Aprendizaje',
  description: 'Plataforma de aprendizaje y evaluación para nuestra organización',
  website: 'https://miorganizacion.com',
  subdomain: 'miorganizacion',
  custom_domain: '',
  primary_color: '#3B82F6',
  secondary_color: '#10B981',
  accent_color: '#F59E0B',
  background_color: '#FFFFFF',
  text_color: '#1F2937',
  logo_url: '',
  logo_light_url: '',
  logo_dark_url: '',
  favicon_url: '',
  login_background_url: '',
  font_family: 'Inter',
  border_radius: '8px',
  header_style: 'default',
  sidebar_style: 'default',
  card_style: 'default',
  button_style: 'default',
  custom_css: '',
  custom_js: '',
  custom_footer: '',
  hide_arroyo_branding: false,
  enable_white_label: false,
};

export default function TenantBrandingPage() {
  const [branding, setBranding] = useState<TenantBranding>(defaultBranding);
  const [activeTab, setActiveTab] = useState<string>('organization');
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [hasChanges, setHasChanges] = useState(false);
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [showPreview, setShowPreview] = useState(false);
  const [copiedSubdomain, setCopiedSubdomain] = useState(false);

  const logoUploadRef = useRef<HTMLInputElement>(null);
  const faviconUploadRef = useRef<HTMLInputElement>(null);
  const backgroundUploadRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    loadBranding();
  }, []);

  const loadBranding = async () => {
    try {
      setIsLoading(true);
      const existingBranding = await brandingApi.getBranding();
      if (existingBranding) {
        setBranding(existingBranding);
      } else {
        // Load defaults if no branding exists
        const defaults = await brandingApi.getDefaults();
        setBranding(defaults);
      }
    } catch (error) {
      console.error('Error loading branding:', error);
      toast.error('Error al cargar la configuración de marca');
    } finally {
      setIsLoading(false);
    }
  };

  const updateBranding = (key: string, value: any) => {
    setBranding(prev => ({
      ...prev,
      [key]: value,
    }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      if (branding.branding_id) {
        // Update existing branding
        await brandingApi.updateBranding(branding);
      } else {
        // Create new branding
        await brandingApi.createBranding(branding);
      }
      setHasChanges(false);
      toast.success('Configuración de marca guardada exitosamente');
      // Reload to get updated data
      await loadBranding();
    } catch (error: any) {
      console.error('Error saving branding:', error);
      toast.error(error.response?.data?.detail || 'Error al guardar la configuración');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = async () => {
    try {
      const defaults = await brandingApi.resetToDefaults();
      setBranding(defaults);
      setHasChanges(false);
      toast.success('Configuración restablecida a valores por defecto');
    } catch (error) {
      console.error('Error resetting branding:', error);
      toast.error('Error al restablecer la configuración');
    }
  };

  const handleFileUpload = async (type: 'logo' | 'favicon' | 'background', file: File) => {
    try {
      let uploadResponse;

      switch (type) {
        case 'logo':
          uploadResponse = await brandingApi.uploadLogo(file);
          updateBranding('logo_url', uploadResponse.url);
          break;
        case 'favicon':
          uploadResponse = await brandingApi.uploadFavicon(file);
          updateBranding('favicon_url', uploadResponse.url);
          break;
        case 'background':
          uploadResponse = await brandingApi.uploadBackground(file);
          updateBranding('login_background_url', uploadResponse.url);
          break;
      }

      toast.success('Archivo subido exitosamente');
    } catch (error: any) {
      console.error('Error uploading file:', error);
      toast.error(error.response?.data?.detail || 'Error al subir el archivo');
    }
  };

  const copySubdomainUrl = async () => {
    try {
      const { preview_url } = await brandingApi.getPreviewUrl();
      navigator.clipboard.writeText(preview_url);
      setCopiedSubdomain(true);
      toast.success('URL copiada al portapapeles');
      setTimeout(() => setCopiedSubdomain(false), 2000);
    } catch (error) {
      const url = `https://${branding.subdomain}.arroyouniversity.com`;
      navigator.clipboard.writeText(url);
      setCopiedSubdomain(true);
      toast.success('URL copiada al portapapeles');
      setTimeout(() => setCopiedSubdomain(false), 2000);
    }
  };

  const generateSubdomain = async () => {
    try {
      const suggestions = await brandingApi.getSubdomainSuggestions(branding.organization_name, 1);
      if (suggestions.length > 0) {
        updateBranding('subdomain', suggestions[0]);
      }
    } catch (error) {
      // Fallback to local generation
      const name = branding.organization_name.toLowerCase()
        .replace(/[^a-z0-9]/g, '')
        .substring(0, 20);
      updateBranding('subdomain', name);
    }
  };

  const checkSubdomainAvailability = async (subdomain: string) => {
    try {
      const result = await brandingApi.checkSubdomainAvailability(subdomain);
      if (!result.is_available && result.suggestions) {
        toast.error(`Subdominio no disponible. Sugerencias: ${result.suggestions.join(', ')}`);
      }
      return result.is_available;
    } catch (error) {
      console.error('Error checking subdomain:', error);
      return true; // Assume available if check fails
    }
  };

  const tabs = [
    { id: 'organization', label: 'Organización', icon: Building },
    { id: 'branding', label: 'Marca y Colores', icon: Palette },
    { id: 'customization', label: 'Personalización', icon: Type },
    { id: 'features', label: 'Características', icon: Globe },
  ];

  const fontOptions = [
    { value: 'Inter', label: 'Inter (Recomendado)' },
    { value: 'Roboto', label: 'Roboto' },
    { value: 'Open Sans', label: 'Open Sans' },
    { value: 'Lato', label: 'Lato' },
    { value: 'Montserrat', label: 'Montserrat' },
    { value: 'Poppins', label: 'Poppins' },
    { value: 'Source Sans Pro', label: 'Source Sans Pro' },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'organization':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nombre de la Organización *
                </label>
                <Input
                  value={branding.organization_name}
                  onChange={(e) => updateBranding('organization_name', e.target.value)}
                  placeholder="Ej: Universidad Tecnológica"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nombre para Mostrar
                </label>
                <Input
                  value={branding.display_name || ''}
                  onChange={(e) => updateBranding('display_name', e.target.value)}
                  placeholder="Ej: Universidad Tecnológica - Campus Virtual"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Descripción
              </label>
              <textarea
                rows={3}
                value={branding.description || ''}
                onChange={(e) => updateBranding('description', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Descripción de tu organización y plataforma de aprendizaje"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sitio Web
              </label>
              <Input
                type="url"
                value={branding.website || ''}
                onChange={(e) => updateBranding('website', e.target.value)}
                placeholder="https://miorganizacion.com"
              />
            </div>

            {/* Subdomain Configuration */}
            <div className="border-t pt-6">
              <h4 className="text-lg font-medium text-gray-900 mb-4">Configuración de Dominio</h4>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Subdominio *
                  </label>
                  <div className="flex items-center space-x-2">
                    <Input
                      value={branding.subdomain}
                      onChange={(e) => updateBranding('subdomain', e.target.value.toLowerCase().replace(/[^a-z0-9]/g, ''))}
                      placeholder="miorganizacion"
                      className="flex-1"
                    />
                    <span className="text-gray-500">.arroyouniversity.com</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={generateSubdomain}
                    >
                      Auto
                    </Button>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    Solo letras minúsculas y números. Este será tu URL único.
                  </p>
                </div>

                {branding.subdomain && (
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-blue-900">Tu URL será:</p>
                        <p className="text-blue-700 font-mono">
                          https://{branding.subdomain}.arroyouniversity.com
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={copySubdomainUrl}
                        >
                          {copiedSubdomain ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(`https://${branding.subdomain}.arroyouniversity.com`, '_blank')}
                        >
                          <ExternalLink className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Dominio Personalizado (Opcional)
                  </label>
                  <Input
                    value={branding.custom_domain || ''}
                    onChange={(e) => updateBranding('custom_domain', e.target.value)}
                    placeholder="campus.miorganizacion.com"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Configura tu propio dominio. Requiere configuración DNS adicional.
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      case 'branding':
        return (
          <div className="space-y-6">
            {/* Color Palette */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">Paleta de Colores</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Color Primario
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="color"
                      value={branding.primary_color}
                      onChange={(e) => updateBranding('primary_color', e.target.value)}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <Input
                      value={branding.primary_color}
                      onChange={(e) => updateBranding('primary_color', e.target.value)}
                      className="flex-1 font-mono text-sm"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Color Secundario
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="color"
                      value={branding.secondary_color}
                      onChange={(e) => updateBranding('secondary_color', e.target.value)}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <Input
                      value={branding.secondary_color}
                      onChange={(e) => updateBranding('secondary_color', e.target.value)}
                      className="flex-1 font-mono text-sm"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Color de Acento
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="color"
                      value={branding.accent_color}
                      onChange={(e) => updateBranding('accent_color', e.target.value)}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <Input
                      value={branding.accent_color}
                      onChange={(e) => updateBranding('accent_color', e.target.value)}
                      className="flex-1 font-mono text-sm"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Fondo
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="color"
                      value={branding.background_color}
                      onChange={(e) => updateBranding('background_color', e.target.value)}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <Input
                      value={branding.background_color}
                      onChange={(e) => updateBranding('background_color', e.target.value)}
                      className="flex-1 font-mono text-sm"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Texto
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="color"
                      value={branding.text_color}
                      onChange={(e) => updateBranding('text_color', e.target.value)}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <Input
                      value={branding.text_color}
                      onChange={(e) => updateBranding('text_color', e.target.value)}
                      className="flex-1 font-mono text-sm"
                    />
                  </div>
                </div>
              </div>

              {/* Color Preview */}
              <div className="mt-6 p-4 border rounded-lg" style={{ backgroundColor: branding.background_color }}>
                <h5 className="font-medium mb-2" style={{ color: branding.text_color }}>
                  Vista Previa de Colores
                </h5>
                <div className="flex items-center space-x-2">
                  <button
                    className="px-4 py-2 rounded text-white font-medium"
                    style={{ backgroundColor: branding.primary_color }}
                  >
                    Botón Primario
                  </button>
                  <button
                    className="px-4 py-2 rounded text-white font-medium"
                    style={{ backgroundColor: branding.secondary_color }}
                  >
                    Botón Secundario
                  </button>
                  <span
                    className="px-2 py-1 rounded text-white text-sm"
                    style={{ backgroundColor: branding.accent_color }}
                  >
                    Acento
                  </span>
                </div>
              </div>
            </div>

            {/* Logo Upload */}
            <div className="border-t pt-6">
              <h4 className="text-lg font-medium text-gray-900 mb-4">Logotipos</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Logo Principal
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                    {branding.logo_url ? (
                      <div className="space-y-2">
                        <img
                          src={branding.logo_url}
                          alt="Logo"
                          className="max-h-20 max-w-full object-contain mx-auto"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => updateBranding('logo_url', '')}
                        >
                          Remover
                        </Button>
                      </div>
                    ) : (
                      <div>
                        <Image className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                        <p className="text-sm text-gray-500">Subir logo principal</p>
                        <p className="text-xs text-gray-400">PNG, JPG hasta 5MB</p>
                        <input
                          ref={logoUploadRef}
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleFileUpload('logo', file);
                          }}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => logoUploadRef.current?.click()}
                          className="mt-2"
                        >
                          <Upload className="w-4 h-4 mr-2" />
                          Subir Logo
                        </Button>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Favicon
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                    {branding.favicon_url ? (
                      <div className="space-y-2">
                        <img
                          src={branding.favicon_url}
                          alt="Favicon"
                          className="w-8 h-8 mx-auto object-contain"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => updateBranding('favicon_url', '')}
                        >
                          Remover
                        </Button>
                      </div>
                    ) : (
                      <div>
                        <Image className="w-6 h-6 mx-auto text-gray-400 mb-2" />
                        <p className="text-sm text-gray-500">Subir favicon</p>
                        <p className="text-xs text-gray-400">ICO, PNG 32x32px</p>
                        <input
                          ref={faviconUploadRef}
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleFileUpload('favicon', file);
                          }}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => faviconUploadRef.current?.click()}
                          className="mt-2"
                        >
                          <Upload className="w-4 h-4 mr-2" />
                          Subir Favicon
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'customization':
        return (
          <div className="space-y-6">
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">Tipografía y Estilo</h4>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Familia de Fuente
                  </label>
                  <select
                    value={branding.font_family}
                    onChange={(e) => updateBranding('font_family', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {fontOptions.map(font => (
                      <option key={font.value} value={font.value}>
                        {font.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Radio de Bordes
                  </label>
                  <select
                    value={branding.border_radius}
                    onChange={(e) => updateBranding('border_radius', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="4px">Pequeño (4px)</option>
                    <option value="8px">Mediano (8px)</option>
                    <option value="12px">Grande (12px)</option>
                    <option value="16px">Extra Grande (16px)</option>
                  </select>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">Estilos de Componentes</h4>

              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Estilo del Header
                  </label>
                  <div className="space-y-2">
                    {[
                      { value: 'default', label: 'Por Defecto' },
                      { value: 'minimal', label: 'Minimalista' },
                      { value: 'bold', label: 'Destacado' }
                    ].map(option => (
                      <label key={option.value} className="flex items-center">
                        <input
                          type="radio"
                          name="headerStyle"
                          value={option.value}
                          checked={branding.header_style === option.value}
                          onChange={(e) => updateBranding('header_style', e.target.value)}
                          className="mr-2"
                        />
                        {option.label}
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Estilo de la Barra Lateral
                  </label>
                  <div className="space-y-2">
                    {[
                      { value: 'default', label: 'Por Defecto' },
                      { value: 'compact', label: 'Compacta' },
                      { value: 'expanded', label: 'Expandida' }
                    ].map(option => (
                      <label key={option.value} className="flex items-center">
                        <input
                          type="radio"
                          name="sidebarStyle"
                          value={option.value}
                          checked={branding.sidebar_style === option.value}
                          onChange={(e) => updateBranding('sidebar_style', e.target.value)}
                          className="mr-2"
                        />
                        {option.label}
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'features':
        return (
          <div className="space-y-6">
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">Características Avanzadas</h4>

              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div>
                    <h5 className="text-sm font-medium text-gray-900">Ocultar Marca Arroyo</h5>
                    <p className="text-sm text-gray-500">Oculta la marca "Powered by Arroyo University"</p>
                  </div>
                  <Switch
                    checked={branding.hide_arroyo_branding}
                    onCheckedChange={(checked) => updateBranding('hide_arroyo_branding', checked)}
                  />
                </div>

                <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div>
                    <h5 className="text-sm font-medium text-gray-900">Habilitar White Label</h5>
                    <p className="text-sm text-gray-500">Personalización completa sin referencias a Arroyo</p>
                  </div>
                  <Switch
                    checked={branding.enable_white_label}
                    onCheckedChange={(checked) => updateBranding('enable_white_label', checked)}
                  />
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">CSS y JavaScript Personalizado</h4>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    CSS Personalizado
                  </label>
                  <textarea
                    rows={8}
                    value={branding.custom_css || ''}
                    onChange={(e) => updateBranding('custom_css', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                    placeholder="/* Agrega tu CSS personalizado aquí */
.custom-header {
  background: linear-gradient(45deg, #your-color);
}"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    JavaScript Personalizado
                  </label>
                  <textarea
                    rows={6}
                    value={branding.custom_js || ''}
                    onChange={(e) => updateBranding('custom_js', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                    placeholder="// Agrega tu JavaScript personalizado aquí
console.log('Custom JS loaded');"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Footer Personalizado
                  </label>
                  <textarea
                    rows={4}
                    value={branding.custom_footer || ''}
                    onChange={(e) => updateBranding('custom_footer', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="HTML para el footer personalizado"
                  />
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return <div>Contenido no disponible</div>;
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div className="space-y-2">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="h-10 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="lg:col-span-3">
              <div className="h-96 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Configuración de Marca</h1>
            <p className="text-gray-600 mt-1">
              Personaliza la apariencia y marca de tu plataforma de aprendizaje
            </p>
          </div>
          <div className="flex items-center space-x-3">
            {hasChanges && (
              <Badge variant="yellow">Cambios sin guardar</Badge>
            )}
            <Button
              variant="outline"
              onClick={() => setShowPreview(!showPreview)}
            >
              <Eye className="w-4 h-4 mr-2" />
              {showPreview ? 'Ocultar' : 'Vista Previa'}
            </Button>
            <Button
              variant="outline"
              onClick={handleReset}
              disabled={isSaving}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Restablecer
            </Button>
            <Button
              onClick={handleSave}
              disabled={isSaving || !hasChanges}
            >
              <Save className="w-4 h-4 mr-2" />
              {isSaving ? 'Guardando...' : 'Guardar Cambios'}
            </Button>
          </div>
        </div>
      </div>

      {/* Preview Mode Toggle */}
      {showPreview && (
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-blue-900">Modo Vista Previa</h3>
              <p className="text-sm text-blue-700">Ve cómo se verá tu plataforma con los cambios aplicados</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant={previewMode === 'desktop' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPreviewMode('desktop')}
              >
                <Monitor className="w-4 h-4" />
              </Button>
              <Button
                variant={previewMode === 'tablet' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPreviewMode('tablet')}
              >
                <Tablet className="w-4 h-4" />
              </Button>
              <Button
                variant={previewMode === 'mobile' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPreviewMode('mobile')}
              >
                <Smartphone className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <nav className="space-y-1">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as keyof TenantBranding)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  {tab.label}
                </button>
              );
            })}
          </nav>

          {/* Quick Actions */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Acciones Rápidas</h4>
            <div className="space-y-2">
              <Button
                variant="outline"
                size="sm"
                className="w-full justify-start"
                onClick={async () => {
                  try {
                    const blob = await brandingApi.exportBranding();
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'tenant-branding-config.json';
                    a.click();
                    URL.revokeObjectURL(url);
                    toast.success('Configuración exportada');
                  } catch (error) {
                    toast.error('Error al exportar configuración');
                  }
                }}
              >
                <Download className="w-4 h-4 mr-2" />
                Exportar Config
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="w-full justify-start"
                onClick={() => window.open(`https://${branding.subdomain}.arroyouniversity.com`, '_blank')}
                disabled={!branding.subdomain}
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Ver Sitio
              </Button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                {tabs.find(t => t.id === activeTab)?.icon && (
                  React.createElement(tabs.find(t => t.id === activeTab)!.icon, {
                    className: "w-5 h-5 mr-2"
                  })
                )}
                {tabs.find(t => t.id === activeTab)?.label}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {renderTabContent()}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

