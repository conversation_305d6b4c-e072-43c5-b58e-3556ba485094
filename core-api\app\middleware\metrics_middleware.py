"""
Metrics middleware for automatic request tracking
"""

import time
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from app.core.metrics import record_http_request, update_system_metrics
import logging

logger = logging.getLogger(__name__)


class MetricsMiddleware(BaseHTTPMiddleware):
    """Middleware to automatically collect HTTP request metrics"""
    
    async def dispatch(self, request: Request, call_next):
        # Skip metrics collection for the metrics endpoint itself
        if request.url.path == "/metrics":
            return await call_next(request)
        
        # Record start time
        start_time = time.time()
        
        # Process request
        try:
            response = await call_next(request)
            status_code = response.status_code
        except Exception as e:
            # Record error metrics
            duration = time.time() - start_time
            record_http_request(
                method=request.method,
                endpoint=self._get_endpoint_pattern(request),
                status_code=500,
                duration=duration
            )
            logger.error(f"Request failed: {e}")
            raise
        
        # Calculate duration
        duration = time.time() - start_time
        
        # Record metrics
        record_http_request(
            method=request.method,
            endpoint=self._get_endpoint_pattern(request),
            status_code=status_code,
            duration=duration
        )
        
        # Update system metrics periodically (every 10th request)
        if hash(str(time.time())) % 10 == 0:
            try:
                update_system_metrics()
            except Exception as e:
                logger.warning(f"Failed to update system metrics: {e}")
        
        return response
    
    def _get_endpoint_pattern(self, request: Request) -> str:
        """
        Extract endpoint pattern from request path
        This helps group similar endpoints together in metrics
        """
        path = request.url.path
        
        # Common patterns to normalize
        patterns = [
            (r'/api/v1/users/[^/]+', '/api/v1/users/{id}'),
            (r'/api/v1/courses/[^/]+', '/api/v1/courses/{id}'),
            (r'/api/v1/exams/[^/]+', '/api/v1/exams/{id}'),
            (r'/api/v1/questions/[^/]+', '/api/v1/questions/{id}'),
            (r'/api/v1/groups/[^/]+', '/api/v1/groups/{id}'),
            (r'/api/v1/tenants/[^/]+', '/api/v1/tenants/{id}'),
        ]
        
        import re
        for pattern, replacement in patterns:
            if re.match(pattern, path):
                return replacement
        
        return path
