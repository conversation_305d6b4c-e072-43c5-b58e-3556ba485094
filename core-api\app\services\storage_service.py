"""
Storage service for file uploads using MinIO
"""

import os
import uuid
from typing import Optional
from io import BytesIO
from minio import Minio
from minio.error import S3Error
import logging
from fastapi import HTTPException, UploadFile
from app.core.config import settings

logger = logging.getLogger(__name__)


class StorageService:
    """MinIO storage service for file uploads"""
    
    def __init__(self):
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize MinIO client"""
        try:
            # MinIO configuration from environment
            endpoint = os.getenv('MINIO_ENDPOINT', 'minio:9000')
            access_key = os.getenv('MINIO_ROOT_USER', 'minioadmin')
            secret_key = os.getenv('MINIO_ROOT_PASSWORD', 'minioadmin123')
            secure = os.getenv('MINIO_SECURE', 'false').lower() == 'true'
            
            self.client = Minio(
                endpoint=endpoint,
                access_key=access_key,
                secret_key=secret_key,
                secure=secure
            )
            
            # Create default buckets if they don't exist
            self._ensure_buckets_exist()
            
            logger.info("MinIO client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize MinIO client: {e}")
            self.client = None
    
    def _ensure_buckets_exist(self):
        """Ensure required buckets exist"""
        buckets = [
            'tenant-logos',
            'tenant-favicons', 
            'tenant-backgrounds',
            'course-materials',
            'exam-files',
            'user-uploads',
            'ai-generated-audio'
        ]
        
        for bucket in buckets:
            try:
                if not self.client.bucket_exists(bucket):
                    self.client.make_bucket(bucket)
                    logger.info(f"Created bucket: {bucket}")
            except S3Error as e:
                logger.error(f"Error creating bucket {bucket}: {e}")
    
    def is_available(self) -> bool:
        """Check if storage service is available"""
        return self.client is not None
    
    async def upload_file(
        self,
        file: UploadFile,
        bucket: str,
        tenant_id: str,
        file_type: str = "general"
    ) -> dict:
        """
        Upload a file to MinIO storage
        
        Args:
            file: FastAPI UploadFile object
            bucket: MinIO bucket name
            tenant_id: Tenant ID for organization
            file_type: Type of file for organization
            
        Returns:
            dict: File information including URL and metadata
        """
        if not self.is_available():
            raise HTTPException(
                status_code=503,
                detail="Storage service is not available"
            )
        
        try:
            # Generate unique filename
            file_extension = os.path.splitext(file.filename)[1] if file.filename else ''
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            object_name = f"{tenant_id}/{file_type}/{unique_filename}"
            
            # Read file content
            file_content = await file.read()
            file_size = len(file_content)
            
            # Upload to MinIO
            self.client.put_object(
                bucket_name=bucket,
                object_name=object_name,
                data=BytesIO(file_content),
                length=file_size,
                content_type=file.content_type or 'application/octet-stream'
            )
            
            # Generate public URL (for development)
            # In production, you might want to use presigned URLs
            file_url = f"http://localhost:9000/{bucket}/{object_name}"
            
            logger.info(f"File uploaded successfully: {object_name}")
            
            return {
                "url": file_url,
                "filename": file.filename,
                "unique_filename": unique_filename,
                "size": file_size,
                "content_type": file.content_type,
                "bucket": bucket,
                "object_name": object_name
            }
            
        except S3Error as e:
            logger.error(f"MinIO error during upload: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Storage error: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error during upload: {e}")
            raise HTTPException(
                status_code=500,
                detail="Failed to upload file"
            )
    
    async def delete_file(self, bucket: str, object_name: str) -> bool:
        """
        Delete a file from MinIO storage
        
        Args:
            bucket: MinIO bucket name
            object_name: Object name in bucket
            
        Returns:
            bool: True if deleted successfully
        """
        if not self.is_available():
            return False
        
        try:
            self.client.remove_object(bucket, object_name)
            logger.info(f"File deleted successfully: {object_name}")
            return True
        except S3Error as e:
            logger.error(f"Error deleting file {object_name}: {e}")
            return False
    
    async def get_file_url(self, bucket: str, object_name: str, expires: int = 3600) -> Optional[str]:
        """
        Get a presigned URL for a file
        
        Args:
            bucket: MinIO bucket name
            object_name: Object name in bucket
            expires: URL expiration time in seconds
            
        Returns:
            str: Presigned URL or None if error
        """
        if not self.is_available():
            return None
        
        try:
            url = self.client.presigned_get_object(bucket, object_name, expires=expires)
            return url
        except S3Error as e:
            logger.error(f"Error generating presigned URL for {object_name}: {e}")
            return None
    
    async def upload_logo(self, file: UploadFile, tenant_id: str) -> dict:
        """Upload tenant logo"""
        return await self.upload_file(file, 'tenant-logos', tenant_id, 'logo')
    
    async def upload_favicon(self, file: UploadFile, tenant_id: str) -> dict:
        """Upload tenant favicon"""
        return await self.upload_file(file, 'tenant-favicons', tenant_id, 'favicon')
    
    async def upload_background(self, file: UploadFile, tenant_id: str) -> dict:
        """Upload tenant background image"""
        return await self.upload_file(file, 'tenant-backgrounds', tenant_id, 'background')


# Global storage service instance
storage_service = StorageService()


def get_storage_service() -> StorageService:
    """Get storage service instance"""
    return storage_service
