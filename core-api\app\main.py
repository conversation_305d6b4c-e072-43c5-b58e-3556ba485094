"""
Arroyo University Core API
Main FastAPI application entry point
"""

from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging
from contextlib import asynccontextmanager

# Import configuration and database
from .core.config import settings
from .core.database import create_db_and_tables

# Import routers
from .routers import (
    auth_router, users_router, courses_router,
    exams_router, questions_router, ai_router,
    forums_router, groups_router, analytics_router, settings_router,
    tenant_notifications_router, tenant_branding_router
)

# Import middleware
from .middleware.metrics_middleware import MetricsMiddleware

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting Arroyo University Core API")
    create_db_and_tables()
    yield
    # Shutdown
    logger.info("Shutting down Arroyo University Core API")


# Create FastAPI application
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="Arroyo University Core API - Educational Assessment Platform",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
    allow_methods=settings.CORS_ALLOW_METHODS,
    allow_headers=settings.CORS_ALLOW_HEADERS,
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.DEBUG else ["localhost", "127.0.0.1"]
)

# Add metrics middleware
app.add_middleware(MetricsMiddleware)


# Exception handlers
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(f"Global exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "type": "https://api.arroyo.app/errors/internal-server-error",
            "title": "Internal Server Error",
            "status": 500,
            "detail": "An unexpected error occurred" if not settings.DEBUG else str(exc),
            "instance": str(request.url)
        }
    )


# Health check endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "core-api"}


@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint"""
    # Add database connectivity check here
    return {"status": "ready", "service": "core-api"}


# Metrics endpoint (for Prometheus)
@app.get("/metrics", include_in_schema=False)
async def metrics():
    """Metrics endpoint for Prometheus"""
    from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
    from app.core.metrics import get_metrics_registry

    registry = get_metrics_registry()
    return Response(
        content=generate_latest(registry),
        media_type=CONTENT_TYPE_LATEST
    )


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Arroyo University Core API",
        "version": settings.APP_VERSION,
        "docs": "/docs" if settings.DEBUG else "Documentation not available in production"
    }


# Include routers
app.include_router(auth_router.router, prefix="/api/v1/auth", tags=["authentication"])
app.include_router(users_router.router, prefix="/api/v1/users", tags=["users"])
app.include_router(courses_router.router, prefix="/api/v1/courses", tags=["courses"])
app.include_router(exams_router.router, prefix="/api/v1/exams", tags=["exams"])
app.include_router(questions_router.router, prefix="/api/v1/questions", tags=["questions"])
app.include_router(ai_router.router, prefix="/api/v1/ai", tags=["ai"])
app.include_router(forums_router.router, prefix="/api/v1/forums", tags=["forums"])
app.include_router(groups_router.router, prefix="/api/v1/groups", tags=["groups"])
app.include_router(analytics_router.router, prefix="/api/v1/analytics", tags=["analytics"])
app.include_router(settings_router.router, prefix="/api/v1/settings", tags=["settings"])
app.include_router(tenant_notifications_router.router, prefix="/api/v1/tenant/notifications", tags=["tenant-notifications"])
app.include_router(tenant_branding_router.router, prefix="/api/v1/tenant/branding", tags=["tenant-branding"])


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
