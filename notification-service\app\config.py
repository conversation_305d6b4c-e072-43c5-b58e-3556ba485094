"""
Arroyo University Notification Service Configuration
"""

from pydantic_settings import BaseSettings
from pydantic import Field
from typing import List, Dict


class Settings(BaseSettings):
    """Notification Service settings"""

    # Application
    APP_NAME: str = Field(default="Arroyo University Notification Service")
    APP_VERSION: str = Field(default="1.0.0")
    DEBUG: bool = Field(default=True)
    ENVIRONMENT: str = Field(default="development")

    # Database
    DATABASE_URL: str = Field(...)

    # Redis
    REDIS_URL: str = Field(...)

    # Tenant Configuration
    ENABLE_TENANT_CONFIGS: bool = Field(default=True)
    CONFIG_ENCRYPTION_KEY: str = Field(default="")  # For encrypting sensitive tenant config data
    FALLBACK_TO_GLOBAL_CONFIG: bool = Field(default=True)  # Use global config if tenant config not found
    
    # Global Email (SMTP) - Used as fallback when tenant config not available
    GLOBAL_SMTP_HOST: str = Field(default="smtp.gmail.com")
    GLOBAL_SMTP_PORT: int = Field(default=587)
    GLOBAL_SMTP_USER: str = Field(default="")
    GLOBAL_SMTP_PASSWORD: str = Field(default="")
    GLOBAL_SMTP_TLS: bool = Field(default=True)
    GLOBAL_SMTP_SSL: bool = Field(default=False)
    GLOBAL_SMTP_TIMEOUT: int = Field(default=30)
    GLOBAL_FROM_EMAIL: str = Field(default="<EMAIL>")
    GLOBAL_FROM_NAME: str = Field(default="Arroyo University")

    # Legacy Email Settings (for backward compatibility)
    SMTP_HOST: str = Field(default="smtp.gmail.com")
    SMTP_PORT: int = Field(default=587)
    SMTP_USER: str = Field(default="")
    SMTP_PASSWORD: str = Field(default="")
    SMTP_TLS: bool = Field(default=True)
    SMTP_SSL: bool = Field(default=False)
    SMTP_TIMEOUT: int = Field(default=30)
    FROM_EMAIL: str = Field(default="<EMAIL>")
    FROM_NAME: str = Field(default="Arroyo University")
    
    # Email Templates
    TEMPLATE_DIR: str = Field(default="/app/templates")
    DEFAULT_LANGUAGE: str = Field(default="en")
    SUPPORTED_LANGUAGES: List[str] = Field(default=["en", "es", "fr", "de"])
    
    # Push Notifications
    ENABLE_PUSH_NOTIFICATIONS: bool = Field(default=True)
    FCM_SERVER_KEY: str = Field(default="")
    FCM_SENDER_ID: str = Field(default="")
    APNS_KEY_ID: str = Field(default="")
    APNS_TEAM_ID: str = Field(default="")
    APNS_BUNDLE_ID: str = Field(default="com.arroyo.university")
    
    # Webhook
    WEBHOOK_TIMEOUT: int = Field(default=30)
    WEBHOOK_MAX_RETRIES: int = Field(default=3)
    WEBHOOK_RETRY_DELAY: int = Field(default=5)
    WEBHOOK_SECRET: str = Field(default="")
    
    # Global SMS (Twilio) - Used as fallback
    GLOBAL_ENABLE_SMS: bool = Field(default=False)
    GLOBAL_TWILIO_ACCOUNT_SID: str = Field(default="")
    GLOBAL_TWILIO_AUTH_TOKEN: str = Field(default="")
    GLOBAL_TWILIO_PHONE_NUMBER: str = Field(default="")

    # Global Slack - Used as fallback
    GLOBAL_ENABLE_SLACK_NOTIFICATIONS: bool = Field(default=True)
    GLOBAL_SLACK_BOT_TOKEN: str = Field(default="")
    GLOBAL_SLACK_SIGNING_SECRET: str = Field(default="")
    GLOBAL_DEFAULT_SLACK_CHANNEL: str = Field(default="#general")

    # Global Microsoft Teams - Used as fallback
    GLOBAL_ENABLE_TEAMS_NOTIFICATIONS: bool = Field(default=False)
    GLOBAL_TEAMS_WEBHOOK_URL: str = Field(default="")

    # Legacy Settings (for backward compatibility)
    ENABLE_SMS: bool = Field(default=False)
    TWILIO_ACCOUNT_SID: str = Field(default="")
    TWILIO_AUTH_TOKEN: str = Field(default="")
    TWILIO_PHONE_NUMBER: str = Field(default="")

    ENABLE_SLACK_NOTIFICATIONS: bool = Field(default=True)
    SLACK_BOT_TOKEN: str = Field(default="")
    SLACK_SIGNING_SECRET: str = Field(default="")
    DEFAULT_SLACK_CHANNEL: str = Field(default="#general")

    ENABLE_TEAMS_NOTIFICATIONS: bool = Field(default=False)
    TEAMS_WEBHOOK_URL: str = Field(default="")
    
    # WebSocket
    ENABLE_WEBSOCKET: bool = Field(default=True)
    WEBSOCKET_HEARTBEAT_INTERVAL: int = Field(default=30)
    WEBSOCKET_MAX_CONNECTIONS: int = Field(default=1000)
    
    # Queues
    EMAIL_QUEUE_NAME: str = Field(default="email_notifications")
    PUSH_QUEUE_NAME: str = Field(default="push_notifications")
    SMS_QUEUE_NAME: str = Field(default="sms_notifications")
    WEBHOOK_QUEUE_NAME: str = Field(default="webhook_notifications")
    
    # Rate Limiting
    EMAIL_RATE_LIMIT_PER_HOUR: int = Field(default=100)
    PUSH_RATE_LIMIT_PER_HOUR: int = Field(default=1000)
    SMS_RATE_LIMIT_PER_HOUR: int = Field(default=50)
    WEBHOOK_RATE_LIMIT_PER_MINUTE: int = Field(default=60)
    
    # Retry Configuration
    MAX_RETRY_ATTEMPTS: int = Field(default=3)
    RETRY_BACKOFF_FACTOR: int = Field(default=2)
    RETRY_MAX_DELAY: int = Field(default=300)
    
    # Notification Preferences
    DEFAULT_EMAIL_ENABLED: bool = Field(default=True)
    DEFAULT_PUSH_ENABLED: bool = Field(default=True)
    DEFAULT_SMS_ENABLED: bool = Field(default=False)
    ALLOW_USER_PREFERENCES: bool = Field(default=True)
    
    # Content Filtering
    ENABLE_CONTENT_FILTERING: bool = Field(default=True)
    SPAM_FILTER_THRESHOLD: float = Field(default=0.8)
    PROFANITY_FILTER_ENABLED: bool = Field(default=True)
    
    # Analytics
    ENABLE_DELIVERY_TRACKING: bool = Field(default=True)
    ENABLE_OPEN_TRACKING: bool = Field(default=True)
    ENABLE_CLICK_TRACKING: bool = Field(default=True)
    ANALYTICS_RETENTION_DAYS: int = Field(default=90)
    
    # Security
    ENABLE_EMAIL_ENCRYPTION: bool = Field(default=False)
    ENCRYPTION_KEY: str = Field(default="")
    SIGNATURE_SECRET: str = Field(default="")
    
    # Monitoring
    ENABLE_METRICS: bool = Field(default=True)
    METRICS_PORT: int = Field(default=8001)
    SENTRY_DSN: str = Field(default="")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO")
    LOG_FORMAT: str = Field(default="json")
    LOG_FILE: str = Field(default="/app/logs/notification-service.log")
    
    # Performance
    MAX_CONCURRENT_EMAILS: int = Field(default=10)
    MAX_CONCURRENT_PUSH: int = Field(default=50)
    EMAIL_BATCH_SIZE: int = Field(default=100)
    PUSH_BATCH_SIZE: int = Field(default=1000)
    
    # Feature Flags
    ENABLE_EMAIL_NOTIFICATIONS: bool = Field(default=True)
    ENABLE_PUSH_NOTIFICATIONS: bool = Field(default=True)
    ENABLE_SMS_NOTIFICATIONS: bool = Field(default=False)
    ENABLE_WEBHOOK_NOTIFICATIONS: bool = Field(default=True)
    ENABLE_SLACK_NOTIFICATIONS: bool = Field(default=True)
    ENABLE_TEAMS_NOTIFICATIONS: bool = Field(default=False)
    
    # Cleanup
    CLEANUP_OLD_NOTIFICATIONS: bool = Field(default=True)
    NOTIFICATION_RETENTION_DAYS: int = Field(default=30)
    CLEANUP_INTERVAL_HOURS: int = Field(default=24)
    
    # External Services
    CORE_API_URL: str = Field(default="http://core-api:8000")
    AI_SERVICE_URL: str = Field(default="http://ai-service:8000")
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()
