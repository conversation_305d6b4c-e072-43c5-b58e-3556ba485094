"""
Tenant branding configuration service
"""

import re
import json
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from uuid import UUID, uuid4

from sqlmodel import Session, select
from fastapi import HTTPException

from ..models.tenant_branding import (
    TenantBranding, TenantBrandingCreate, TenantBrandingUpdate,
    TenantBrandingHistory, SubdomainAvailability, BrandingValidation,
    BrandingPreview, DEFAULT_BRANDING, CSS_VARIABLES_TEMPLATE
)
from ..models.tenant import Tenant


class TenantBrandingService:
    """Service for managing tenant branding configurations"""
    
    def __init__(self, session: Session):
        self.session = session
    
    async def get_branding(self, tenant_id: UUID) -> Optional[TenantBranding]:
        """Get tenant branding configuration"""
        
        query = select(TenantBranding).where(
            TenantBranding.tenant_id == tenant_id,
            TenantBranding.is_active == True
        )
        
        result = self.session.exec(query)
        return result.first()
    
    async def create_branding(
        self, 
        tenant_id: UUID, 
        branding_data: TenantBrandingCreate,
        user_id: UUID
    ) -> TenantBranding:
        """Create tenant branding configuration"""
        
        # Validate subdomain availability
        if not await self.is_subdomain_available(branding_data.subdomain, tenant_id):
            raise HTTPException(
                status_code=400, 
                detail=f"Subdomain '{branding_data.subdomain}' is not available"
            )
        
        # Validate branding data
        validation = await self.validate_branding(branding_data.model_dump())
        if not validation.is_valid:
            raise HTTPException(
                status_code=400,
                detail=f"Branding validation failed: {', '.join(validation.errors)}"
            )
        
        # Deactivate existing branding
        await self._deactivate_existing_branding(tenant_id)
        
        # Create new branding
        branding = TenantBranding(
            branding_id=uuid4(),
            tenant_id=tenant_id,
            applied_by=user_id,
            last_applied_at=datetime.utcnow(),
            **branding_data.model_dump()
        )
        
        self.session.add(branding)
        self.session.commit()
        self.session.refresh(branding)
        
        # Create history entry
        await self._create_history_entry(branding, user_id, "Created initial branding")
        
        # Update tenant subdomain
        await self._update_tenant_subdomain(tenant_id, branding_data.subdomain)
        
        return branding
    
    async def update_branding(
        self, 
        tenant_id: UUID, 
        branding_data: TenantBrandingUpdate,
        user_id: UUID
    ) -> Optional[TenantBranding]:
        """Update tenant branding configuration"""
        
        # Get existing branding
        branding = await self.get_branding(tenant_id)
        if not branding:
            raise HTTPException(status_code=404, detail="Branding configuration not found")
        
        # Validate subdomain if changed
        update_data = branding_data.model_dump(exclude_unset=True)
        if "subdomain" in update_data:
            if not await self.is_subdomain_available(update_data["subdomain"], tenant_id):
                raise HTTPException(
                    status_code=400,
                    detail=f"Subdomain '{update_data['subdomain']}' is not available"
                )
        
        # Validate updated branding data
        current_data = branding.model_dump()
        current_data.update(update_data)
        validation = await self.validate_branding(current_data)
        if not validation.is_valid:
            raise HTTPException(
                status_code=400,
                detail=f"Branding validation failed: {', '.join(validation.errors)}"
            )
        
        # Create history entry before update
        await self._create_history_entry(branding, user_id, "Updated branding configuration")
        
        # Update branding
        for field, value in update_data.items():
            setattr(branding, field, value)
        
        branding.updated_at = datetime.utcnow()
        branding.last_applied_at = datetime.utcnow()
        branding.applied_by = user_id
        branding.version += 1
        
        self.session.add(branding)
        self.session.commit()
        self.session.refresh(branding)
        
        # Update tenant subdomain if changed
        if "subdomain" in update_data:
            await self._update_tenant_subdomain(tenant_id, update_data["subdomain"])
        
        return branding
    
    async def delete_branding(self, tenant_id: UUID, user_id: UUID) -> bool:
        """Delete (deactivate) tenant branding configuration"""
        
        branding = await self.get_branding(tenant_id)
        if not branding:
            return False
        
        # Create history entry
        await self._create_history_entry(branding, user_id, "Deleted branding configuration")
        
        # Deactivate instead of delete
        branding.is_active = False
        branding.updated_at = datetime.utcnow()
        
        self.session.add(branding)
        self.session.commit()
        
        return True
    
    async def is_subdomain_available(self, subdomain: str, exclude_tenant_id: Optional[UUID] = None) -> bool:
        """Check if subdomain is available"""
        
        # Validate subdomain format
        if not self._is_valid_subdomain(subdomain):
            return False
        
        # Check against existing tenants
        query = select(Tenant).where(Tenant.domain.like(f"{subdomain}.%"))
        if exclude_tenant_id:
            query = query.where(Tenant.tenant_id != exclude_tenant_id)
        
        result = self.session.exec(query)
        existing_tenant = result.first()
        
        if existing_tenant:
            return False
        
        # Check against existing branding configurations
        query = select(TenantBranding).where(
            TenantBranding.subdomain == subdomain,
            TenantBranding.is_active == True
        )
        if exclude_tenant_id:
            query = query.where(TenantBranding.tenant_id != exclude_tenant_id)
        
        result = self.session.exec(query)
        existing_branding = result.first()
        
        return existing_branding is None
    
    async def get_subdomain_suggestions(self, base_name: str, count: int = 5) -> List[str]:
        """Get subdomain suggestions based on base name"""
        
        base_subdomain = self._sanitize_subdomain(base_name)
        suggestions = []
        
        # Try base name first
        if await self.is_subdomain_available(base_subdomain):
            suggestions.append(base_subdomain)
        
        # Try variations
        for i in range(1, count + 1):
            suggestion = f"{base_subdomain}{i}"
            if await self.is_subdomain_available(suggestion):
                suggestions.append(suggestion)
            
            if len(suggestions) >= count:
                break
        
        # Try with common suffixes
        suffixes = ["app", "platform", "learn", "edu", "academy"]
        for suffix in suffixes:
            suggestion = f"{base_subdomain}-{suffix}"
            if await self.is_subdomain_available(suggestion):
                suggestions.append(suggestion)
            
            if len(suggestions) >= count:
                break
        
        return suggestions[:count]
    
    async def validate_branding(self, branding_data: Dict[str, Any]) -> BrandingValidation:
        """Validate branding configuration"""
        
        validation = BrandingValidation()
        errors = []
        warnings = []
        
        # Validate subdomain
        subdomain = branding_data.get("subdomain", "")
        if not self._is_valid_subdomain(subdomain):
            validation.subdomain_valid = False
            errors.append("Subdomain must contain only lowercase letters and numbers")
        
        # Validate colors
        color_fields = ["primary_color", "secondary_color", "accent_color", "background_color", "text_color"]
        for field in color_fields:
            color = branding_data.get(field, "")
            if color and not self._is_valid_hex_color(color):
                validation.colors_valid = False
                errors.append(f"Invalid hex color format for {field}")
        
        # Validate CSS
        custom_css = branding_data.get("custom_css", "")
        if custom_css:
            css_validation = self._validate_css(custom_css)
            if not css_validation["valid"]:
                validation.css_valid = False
                errors.extend(css_validation["errors"])
                warnings.extend(css_validation["warnings"])
        
        # Validate JavaScript
        custom_js = branding_data.get("custom_js", "")
        if custom_js:
            js_validation = self._validate_javascript(custom_js)
            if not js_validation["valid"]:
                validation.js_valid = False
                errors.extend(js_validation["errors"])
                warnings.extend(js_validation["warnings"])
        
        validation.is_valid = len(errors) == 0
        validation.errors = errors
        validation.warnings = warnings
        
        return validation
    
    async def generate_css(self, tenant_id: UUID) -> str:
        """Generate CSS for tenant branding"""
        
        branding = await self.get_branding(tenant_id)
        if not branding:
            return ""
        
        return CSS_VARIABLES_TEMPLATE.format(
            primary_color=branding.primary_color,
            secondary_color=branding.secondary_color,
            accent_color=branding.accent_color,
            background_color=branding.background_color,
            text_color=branding.text_color,
            font_family=branding.font_family,
            border_radius=branding.border_radius,
            custom_css=branding.custom_css or ""
        )
    
    async def get_branding_history(self, tenant_id: UUID, limit: int = 10) -> List[TenantBrandingHistory]:
        """Get branding change history"""
        
        query = select(TenantBrandingHistory).where(
            TenantBrandingHistory.tenant_id == tenant_id
        ).order_by(TenantBrandingHistory.created_at.desc()).limit(limit)
        
        result = self.session.exec(query)
        return result.all()
    
    def _is_valid_subdomain(self, subdomain: str) -> bool:
        """Validate subdomain format"""
        if not subdomain:
            return False
        
        # Must be 3-63 characters, lowercase letters and numbers only
        pattern = r'^[a-z0-9]{3,63}$'
        return bool(re.match(pattern, subdomain))
    
    def _sanitize_subdomain(self, name: str) -> str:
        """Sanitize name to create valid subdomain"""
        # Convert to lowercase and remove special characters
        subdomain = re.sub(r'[^a-z0-9]', '', name.lower())
        
        # Ensure minimum length
        if len(subdomain) < 3:
            subdomain = f"{subdomain}app"
        
        # Ensure maximum length
        if len(subdomain) > 63:
            subdomain = subdomain[:63]
        
        return subdomain
    
    def _is_valid_hex_color(self, color: str) -> bool:
        """Validate hex color format"""
        pattern = r'^#[0-9A-Fa-f]{6}$'
        return bool(re.match(pattern, color))
    
    def _validate_css(self, css: str) -> Dict[str, Any]:
        """Basic CSS validation"""
        # This is a simplified validation - in production, use a proper CSS parser
        errors = []
        warnings = []
        
        # Check for potentially dangerous CSS
        dangerous_patterns = [
            r'@import',
            r'expression\s*\(',
            r'javascript:',
            r'vbscript:',
            r'data:',
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, css, re.IGNORECASE):
                errors.append(f"Potentially dangerous CSS pattern detected: {pattern}")
        
        # Check for unclosed braces
        open_braces = css.count('{')
        close_braces = css.count('}')
        if open_braces != close_braces:
            warnings.append("Unmatched CSS braces detected")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def _validate_javascript(self, js: str) -> Dict[str, Any]:
        """Basic JavaScript validation"""
        # This is a simplified validation - in production, use a proper JS parser
        errors = []
        warnings = []
        
        # Check for potentially dangerous JavaScript
        dangerous_patterns = [
            r'eval\s*\(',
            r'Function\s*\(',
            r'setTimeout\s*\(',
            r'setInterval\s*\(',
            r'document\.write',
            r'innerHTML\s*=',
            r'outerHTML\s*=',
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, js, re.IGNORECASE):
                warnings.append(f"Potentially risky JavaScript pattern: {pattern}")
        
        return {
            "valid": True,  # Allow JS but with warnings
            "errors": errors,
            "warnings": warnings
        }
    
    async def _deactivate_existing_branding(self, tenant_id: UUID):
        """Deactivate existing branding configurations"""
        
        query = select(TenantBranding).where(
            TenantBranding.tenant_id == tenant_id,
            TenantBranding.is_active == True
        )
        
        result = self.session.exec(query)
        existing_brandings = result.all()
        
        for branding in existing_brandings:
            branding.is_active = False
            branding.updated_at = datetime.utcnow()
            self.session.add(branding)
    
    async def _create_history_entry(self, branding: TenantBranding, user_id: UUID, reason: str):
        """Create branding history entry"""
        
        history = TenantBrandingHistory(
            history_id=uuid4(),
            tenant_id=branding.tenant_id,
            branding_id=branding.branding_id,
            branding_data=branding.model_dump(),
            changed_by=user_id,
            change_reason=reason,
            version=branding.version
        )
        
        self.session.add(history)
    
    async def _update_tenant_subdomain(self, tenant_id: UUID, subdomain: str):
        """Update tenant domain with new subdomain"""
        
        query = select(Tenant).where(Tenant.tenant_id == tenant_id)
        result = self.session.exec(query)
        tenant = result.first()
        
        if tenant:
            tenant.domain = f"{subdomain}.arroyouniversity.com"
            self.session.add(tenant)
            self.session.commit()
