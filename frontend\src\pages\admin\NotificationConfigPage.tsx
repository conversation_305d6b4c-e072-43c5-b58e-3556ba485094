import React, { useState, useEffect } from 'react';
import { 
  Plus, <PERSON>ting<PERSON>, TestTube, ToggleLeft, ToggleRight, 
  Mail, MessageSquare, Phone, Webhook, Bell, AlertCircle,
  CheckCircle, XCircle, Eye, EyeOff, Save, Trash2
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Switch } from '@/components/ui/Switch';
import { Badge } from '@/components/ui/Badge';
import { Modal } from '@/components/ui/Modal';
import toast from 'react-hot-toast';

interface NotificationConfig {
  config_id: string;
  channel_type: 'email' | 'sms' | 'slack' | 'teams' | 'webhook' | 'push';
  name: string;
  description?: string;
  is_enabled: boolean;
  status: 'active' | 'inactive' | 'testing' | 'error';
  priority: number;
  config_data: Record<string, any>;
  rate_limit_per_hour?: number;
  rate_limit_per_day?: number;
  last_test_at?: string;
  last_test_result?: string;
  last_error?: string;
  total_sent: number;
  total_failed: number;
  created_at: string;
}

interface ChannelTemplate {
  name: string;
  description: string;
  required_fields: string[];
  optional_fields: string[];
  field_descriptions: Record<string, string>;
  example_config: Record<string, any>;
}

const channelIcons = {
  email: Mail,
  sms: Phone,
  slack: MessageSquare,
  teams: MessageSquare,
  webhook: Webhook,
  push: Bell,
};

const statusColors = {
  active: 'green',
  inactive: 'gray',
  testing: 'yellow',
  error: 'red',
};

export default function NotificationConfigPage() {
  const [configs, setConfigs] = useState<NotificationConfig[]>([]);
  const [templates, setTemplates] = useState<Record<string, ChannelTemplate>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedConfig, setSelectedConfig] = useState<NotificationConfig | null>(null);
  const [selectedChannelType, setSelectedChannelType] = useState<string>('email');
  const [showSensitiveData, setShowSensitiveData] = useState<Record<string, boolean>>({});

  useEffect(() => {
    loadConfigs();
    loadTemplates();
  }, []);

  const loadConfigs = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockConfigs: NotificationConfig[] = [
        {
          config_id: '1',
          channel_type: 'email',
          name: 'Servidor SMTP Principal',
          description: 'Configuración principal para envío de emails',
          is_enabled: true,
          status: 'active',
          priority: 1,
          config_data: {
            smtp_host: 'smtp.gmail.com',
            smtp_port: 587,
            smtp_user: '<EMAIL>',
            smtp_password: '****',
            from_email: '<EMAIL>',
            from_name: 'Mi Organización',
            smtp_tls: true
          },
          rate_limit_per_hour: 1000,
          total_sent: 15420,
          total_failed: 23,
          created_at: '2024-01-15T10:00:00Z'
        },
        {
          config_id: '2',
          channel_type: 'slack',
          name: 'Slack Workspace',
          description: 'Notificaciones al canal de Slack del equipo',
          is_enabled: false,
          status: 'inactive',
          priority: 2,
          config_data: {
            bot_token: 'xoxb-****',
            default_channel: '#notifications',
            signing_secret: '****'
          },
          rate_limit_per_hour: 100,
          total_sent: 0,
          total_failed: 0,
          created_at: '2024-01-20T14:30:00Z'
        }
      ];
      
      setConfigs(mockConfigs);
    } catch (error) {
      toast.error('Error al cargar las configuraciones');
    } finally {
      setIsLoading(false);
    }
  };

  const loadTemplates = async () => {
    try {
      // Simulate API call
      const mockTemplates = {
        email: {
          name: 'Email (SMTP)',
          description: 'Enviar notificaciones por email usando servidor SMTP',
          required_fields: ['smtp_host', 'smtp_port', 'smtp_user', 'smtp_password', 'from_email'],
          optional_fields: ['smtp_tls', 'smtp_ssl', 'from_name', 'reply_to'],
          field_descriptions: {
            smtp_host: 'Servidor SMTP (ej: smtp.gmail.com)',
            smtp_port: 'Puerto SMTP (587 para TLS, 465 para SSL)',
            smtp_user: 'Usuario/email SMTP',
            smtp_password: 'Contraseña SMTP o contraseña de aplicación',
            from_email: 'Dirección de email remitente',
            from_name: 'Nombre del remitente',
            smtp_tls: 'Habilitar encriptación TLS',
            smtp_ssl: 'Habilitar encriptación SSL'
          },
          example_config: {
            smtp_host: 'smtp.gmail.com',
            smtp_port: 587,
            smtp_user: '<EMAIL>',
            smtp_password: 'tu-contraseña-app',
            from_email: '<EMAIL>',
            from_name: 'Tu Organización',
            smtp_tls: true
          }
        },
        slack: {
          name: 'Slack',
          description: 'Enviar notificaciones a canales de Slack',
          required_fields: ['bot_token'],
          optional_fields: ['default_channel', 'signing_secret'],
          field_descriptions: {
            bot_token: 'Token del Bot de Slack (empieza con xoxb-)',
            default_channel: 'Canal por defecto (ej: #general)',
            signing_secret: 'Secreto de firma para verificación de webhooks'
          },
          example_config: {
            bot_token: 'xoxb-tu-token-de-bot',
            default_channel: '#notificaciones',
            signing_secret: 'tu-secreto-de-firma'
          }
        },
        sms: {
          name: 'SMS (Twilio)',
          description: 'Enviar notificaciones SMS usando Twilio',
          required_fields: ['account_sid', 'auth_token', 'phone_number'],
          optional_fields: ['messaging_service_sid'],
          field_descriptions: {
            account_sid: 'SID de cuenta de Twilio',
            auth_token: 'Token de autenticación de Twilio',
            phone_number: 'Número de teléfono de Twilio (ej: +**********)',
            messaging_service_sid: 'SID del servicio de mensajería (opcional)'
          },
          example_config: {
            account_sid: 'ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
            auth_token: 'tu-token-de-auth',
            phone_number: '+**********'
          }
        },
        webhook: {
          name: 'Webhook Personalizado',
          description: 'Enviar notificaciones a endpoints personalizados',
          required_fields: ['url'],
          optional_fields: ['secret', 'headers', 'method'],
          field_descriptions: {
            url: 'URL del endpoint webhook',
            method: 'Método HTTP (POST, PUT, PATCH)',
            headers: 'Headers personalizados para incluir en las peticiones',
            secret: 'Secreto para verificación de firma del webhook'
          },
          example_config: {
            url: 'https://tu-webhook-endpoint.com/notifications',
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer tu-token'
            },
            secret: 'tu-secreto-webhook'
          }
        }
      };
      
      setTemplates(mockTemplates);
    } catch (error) {
      toast.error('Error al cargar las plantillas');
    }
  };

  const handleToggleConfig = async (configId: string) => {
    try {
      const config = configs.find(c => c.config_id === configId);
      if (!config) return;

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setConfigs(prev => prev.map(c => 
        c.config_id === configId 
          ? { ...c, is_enabled: !c.is_enabled }
          : c
      ));
      
      toast.success(`Configuración ${config.is_enabled ? 'deshabilitada' : 'habilitada'}`);
    } catch (error) {
      toast.error('Error al cambiar el estado de la configuración');
    }
  };

  const handleTestConfig = async (configId: string) => {
    try {
      const config = configs.find(c => c.config_id === configId);
      if (!config) return;

      toast.loading('Probando configuración...', { id: 'test-config' });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate random success/failure
      const success = Math.random() > 0.3;
      
      setConfigs(prev => prev.map(c => 
        c.config_id === configId 
          ? { 
              ...c, 
              last_test_at: new Date().toISOString(),
              last_test_result: success ? 'success' : 'failed',
              last_error: success ? undefined : 'Error de conexión simulado',
              status: success ? 'active' : 'error'
            }
          : c
      ));
      
      if (success) {
        toast.success('Configuración probada exitosamente', { id: 'test-config' });
      } else {
        toast.error('Error al probar la configuración', { id: 'test-config' });
      }
    } catch (error) {
      toast.error('Error al probar la configuración', { id: 'test-config' });
    }
  };

  const handleDeleteConfig = async (configId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar esta configuración?')) {
      return;
    }

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setConfigs(prev => prev.filter(c => c.config_id !== configId));
      toast.success('Configuración eliminada');
    } catch (error) {
      toast.error('Error al eliminar la configuración');
    }
  };

  const toggleSensitiveData = (configId: string) => {
    setShowSensitiveData(prev => ({
      ...prev,
      [configId]: !prev[configId]
    }));
  };

  const getStatusBadge = (status: string) => {
    const color = statusColors[status as keyof typeof statusColors];
    const labels = {
      active: 'Activo',
      inactive: 'Inactivo',
      testing: 'Probando',
      error: 'Error'
    };
    
    return (
      <Badge variant={color as any}>
        {labels[status as keyof typeof labels]}
      </Badge>
    );
  };

  const renderConfigCard = (config: NotificationConfig) => {
    const Icon = channelIcons[config.channel_type];
    const showSensitive = showSensitiveData[config.config_id];
    
    return (
      <Card key={config.config_id} className="relative">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${config.is_enabled ? 'bg-blue-100' : 'bg-gray-100'}`}>
                <Icon className={`w-5 h-5 ${config.is_enabled ? 'text-blue-600' : 'text-gray-400'}`} />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">{config.name}</h3>
                <p className="text-sm text-gray-500">{config.description}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {getStatusBadge(config.status)}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleToggleConfig(config.config_id)}
              >
                {config.is_enabled ? (
                  <ToggleRight className="w-5 h-5 text-green-600" />
                ) : (
                  <ToggleLeft className="w-5 h-5 text-gray-400" />
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="space-y-4">
            {/* Configuration Details */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Prioridad:</span>
                <span className="ml-2 font-medium">{config.priority}</span>
              </div>
              <div>
                <span className="text-gray-500">Tipo:</span>
                <span className="ml-2 font-medium capitalize">{config.channel_type}</span>
              </div>
              <div>
                <span className="text-gray-500">Enviados:</span>
                <span className="ml-2 font-medium text-green-600">{config.total_sent.toLocaleString()}</span>
              </div>
              <div>
                <span className="text-gray-500">Fallidos:</span>
                <span className="ml-2 font-medium text-red-600">{config.total_failed.toLocaleString()}</span>
              </div>
            </div>

            {/* Rate Limits */}
            {config.rate_limit_per_hour && (
              <div className="text-sm">
                <span className="text-gray-500">Límite por hora:</span>
                <span className="ml-2 font-medium">{config.rate_limit_per_hour.toLocaleString()}</span>
              </div>
            )}

            {/* Last Test Result */}
            {config.last_test_at && (
              <div className="flex items-center space-x-2 text-sm">
                {config.last_test_result === 'success' ? (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                ) : (
                  <XCircle className="w-4 h-4 text-red-500" />
                )}
                <span className="text-gray-500">
                  Última prueba: {new Date(config.last_test_at).toLocaleString()}
                </span>
              </div>
            )}

            {/* Error Message */}
            {config.last_error && (
              <div className="flex items-start space-x-2 p-2 bg-red-50 rounded-md">
                <AlertCircle className="w-4 h-4 text-red-500 mt-0.5" />
                <span className="text-sm text-red-700">{config.last_error}</span>
              </div>
            )}

            {/* Configuration Data Preview */}
            <div className="border-t pt-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Configuración</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleSensitiveData(config.config_id)}
                >
                  {showSensitive ? (
                    <EyeOff className="w-4 h-4" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                </Button>
              </div>
              <div className="bg-gray-50 p-3 rounded-md">
                <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                  {JSON.stringify(
                    showSensitive 
                      ? config.config_data 
                      : Object.fromEntries(
                          Object.entries(config.config_data).map(([key, value]) => [
                            key,
                            key.includes('password') || key.includes('token') || key.includes('secret')
                              ? '****'
                              : value
                          ])
                        ),
                    null,
                    2
                  )}
                </pre>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between pt-4 border-t">
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleTestConfig(config.config_id)}
                  disabled={!config.is_enabled}
                >
                  <TestTube className="w-4 h-4 mr-1" />
                  Probar
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedConfig(config);
                    setShowEditModal(true);
                  }}
                >
                  <Settings className="w-4 h-4 mr-1" />
                  Editar
                </Button>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDeleteConfig(config.config_id)}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-64 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Configuración de Notificaciones</h1>
            <p className="text-gray-600 mt-1">
              Configura los canales de notificación para tu organización
            </p>
          </div>
          <Button onClick={() => setShowCreateModal(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Nueva Configuración
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Settings className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Configuraciones</p>
                <p className="text-2xl font-bold text-gray-900">{configs.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Activas</p>
                <p className="text-2xl font-bold text-gray-900">
                  {configs.filter(c => c.is_enabled).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Bell className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Notificaciones Enviadas</p>
                <p className="text-2xl font-bold text-gray-900">
                  {configs.reduce((sum, c) => sum + c.total_sent, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <XCircle className="w-6 h-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Fallidas</p>
                <p className="text-2xl font-bold text-gray-900">
                  {configs.reduce((sum, c) => sum + c.total_failed, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Configurations Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {configs.map(renderConfigCard)}
      </div>

      {/* Empty State */}
      {configs.length === 0 && (
        <div className="text-center py-12">
          <Bell className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No hay configuraciones de notificación
          </h3>
          <p className="text-gray-500 mb-6">
            Comienza creando tu primera configuración de notificaciones
          </p>
          <Button onClick={() => setShowCreateModal(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Crear Primera Configuración
          </Button>
        </div>
      )}

      {/* Create Configuration Modal */}
      <CreateConfigModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        templates={templates}
        onConfigCreated={(newConfig) => {
          setConfigs(prev => [...prev, newConfig]);
          setShowCreateModal(false);
          toast.success('Configuración creada exitosamente');
        }}
      />

      {/* Edit Configuration Modal */}
      {selectedConfig && (
        <EditConfigModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setSelectedConfig(null);
          }}
          config={selectedConfig}
          templates={templates}
          onConfigUpdated={(updatedConfig) => {
            setConfigs(prev => prev.map(c =>
              c.config_id === updatedConfig.config_id ? updatedConfig : c
            ));
            setShowEditModal(false);
            setSelectedConfig(null);
            toast.success('Configuración actualizada exitosamente');
          }}
        />
      )}
    </div>
  );
}

// Create Configuration Modal Component
interface CreateConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  templates: Record<string, ChannelTemplate>;
  onConfigCreated: (config: NotificationConfig) => void;
}

function CreateConfigModal({ isOpen, onClose, templates, onConfigCreated }: CreateConfigModalProps) {
  const [selectedChannelType, setSelectedChannelType] = useState<string>('email');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    priority: 5,
    rate_limit_per_hour: '',
    config_data: {} as Record<string, any>
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const currentTemplate = templates[selectedChannelType];

  useEffect(() => {
    if (currentTemplate) {
      // Initialize form with example config
      setFormData(prev => ({
        ...prev,
        config_data: { ...currentTemplate.example_config }
      }));
    }
  }, [selectedChannelType, currentTemplate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newConfig: NotificationConfig = {
        config_id: Date.now().toString(),
        channel_type: selectedChannelType as any,
        name: formData.name,
        description: formData.description,
        is_enabled: false,
        status: 'inactive',
        priority: formData.priority,
        config_data: formData.config_data,
        rate_limit_per_hour: formData.rate_limit_per_hour ? parseInt(formData.rate_limit_per_hour) : undefined,
        total_sent: 0,
        total_failed: 0,
        created_at: new Date().toISOString()
      };

      onConfigCreated(newConfig);
    } catch (error) {
      toast.error('Error al crear la configuración');
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateConfigData = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      config_data: {
        ...prev.config_data,
        [field]: value
      }
    }));
  };

  if (!isOpen) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Nueva Configuración de Notificación">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Channel Type Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tipo de Canal
          </label>
          <div className="grid grid-cols-2 gap-3">
            {Object.entries(templates).map(([type, template]) => {
              const Icon = channelIcons[type as keyof typeof channelIcons];
              return (
                <button
                  key={type}
                  type="button"
                  onClick={() => setSelectedChannelType(type)}
                  className={`p-3 border rounded-lg flex items-center space-x-2 ${
                    selectedChannelType === type
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span className="text-sm font-medium">{template.name}</span>
                </button>
              );
            })}
          </div>
          {currentTemplate && (
            <p className="text-sm text-gray-500 mt-2">{currentTemplate.description}</p>
          )}
        </div>

        {/* Basic Information */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nombre *
            </label>
            <Input
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Ej: Servidor SMTP Principal"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Prioridad
            </label>
            <Input
              type="number"
              min="1"
              max="10"
              value={formData.priority}
              onChange={(e) => setFormData(prev => ({ ...prev, priority: parseInt(e.target.value) }))}
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Descripción
          </label>
          <textarea
            rows={2}
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Descripción opcional de la configuración"
          />
        </div>

        {/* Rate Limiting */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Límite por Hora (opcional)
          </label>
          <Input
            type="number"
            value={formData.rate_limit_per_hour}
            onChange={(e) => setFormData(prev => ({ ...prev, rate_limit_per_hour: e.target.value }))}
            placeholder="Ej: 1000"
          />
        </div>

        {/* Configuration Fields */}
        {currentTemplate && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Configuración del Canal</h4>
            <div className="space-y-4">
              {currentTemplate.required_fields.map(field => (
                <div key={field}>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {currentTemplate.field_descriptions[field]} *
                  </label>
                  <Input
                    type={field.includes('password') || field.includes('token') || field.includes('secret') ? 'password' : 'text'}
                    value={formData.config_data[field] || ''}
                    onChange={(e) => updateConfigData(field, e.target.value)}
                    required
                  />
                </div>
              ))}

              {currentTemplate.optional_fields.map(field => (
                <div key={field}>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {currentTemplate.field_descriptions[field]}
                  </label>
                  {typeof currentTemplate.example_config[field] === 'boolean' ? (
                    <Switch
                      checked={formData.config_data[field] || false}
                      onCheckedChange={(checked) => updateConfigData(field, checked)}
                    />
                  ) : (
                    <Input
                      type={field.includes('password') || field.includes('token') || field.includes('secret') ? 'password' : 'text'}
                      value={formData.config_data[field] || ''}
                      onChange={(e) => updateConfigData(field, e.target.value)}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-end space-x-3 pt-6 border-t">
          <Button type="button" variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Creando...' : 'Crear Configuración'}
          </Button>
        </div>
      </form>
    </Modal>
  );
}

// Edit Configuration Modal Component
interface EditConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  config: NotificationConfig;
  templates: Record<string, ChannelTemplate>;
  onConfigUpdated: (config: NotificationConfig) => void;
}

function EditConfigModal({ isOpen, onClose, config, templates, onConfigUpdated }: EditConfigModalProps) {
  const [formData, setFormData] = useState({
    name: config.name,
    description: config.description || '',
    priority: config.priority,
    rate_limit_per_hour: config.rate_limit_per_hour?.toString() || '',
    config_data: { ...config.config_data }
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const currentTemplate = templates[config.channel_type];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const updatedConfig: NotificationConfig = {
        ...config,
        name: formData.name,
        description: formData.description,
        priority: formData.priority,
        config_data: formData.config_data,
        rate_limit_per_hour: formData.rate_limit_per_hour ? parseInt(formData.rate_limit_per_hour) : undefined,
      };

      onConfigUpdated(updatedConfig);
    } catch (error) {
      toast.error('Error al actualizar la configuración');
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateConfigData = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      config_data: {
        ...prev.config_data,
        [field]: value
      }
    }));
  };

  if (!isOpen) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Editar Configuración">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nombre *
            </label>
            <Input
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Prioridad
            </label>
            <Input
              type="number"
              min="1"
              max="10"
              value={formData.priority}
              onChange={(e) => setFormData(prev => ({ ...prev, priority: parseInt(e.target.value) }))}
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Descripción
          </label>
          <textarea
            rows={2}
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Límite por Hora
          </label>
          <Input
            type="number"
            value={formData.rate_limit_per_hour}
            onChange={(e) => setFormData(prev => ({ ...prev, rate_limit_per_hour: e.target.value }))}
          />
        </div>

        {/* Configuration Fields */}
        {currentTemplate && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Configuración del Canal</h4>
            <div className="space-y-4">
              {[...currentTemplate.required_fields, ...currentTemplate.optional_fields].map(field => (
                <div key={field}>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {currentTemplate.field_descriptions[field]}
                    {currentTemplate.required_fields.includes(field) && ' *'}
                  </label>
                  {typeof formData.config_data[field] === 'boolean' ? (
                    <Switch
                      checked={formData.config_data[field] || false}
                      onCheckedChange={(checked) => updateConfigData(field, checked)}
                    />
                  ) : (
                    <Input
                      type={field.includes('password') || field.includes('token') || field.includes('secret') ? 'password' : 'text'}
                      value={formData.config_data[field] || ''}
                      onChange={(e) => updateConfigData(field, e.target.value)}
                      required={currentTemplate.required_fields.includes(field)}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-end space-x-3 pt-6 border-t">
          <Button type="button" variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Guardando...' : 'Guardar Cambios'}
          </Button>
        </div>
      </form>
    </Modal>
  );
}
